import { useEffect, useState, useRef } from 'react';
import { Keyboard, KeyboardEvent, Platform, Dimensions } from 'react-native';

interface KeyboardState {
  isVisible: boolean;
  height: number;
  animationDuration: number;
  animationEasing: string;
}

export const useKeyboardHandler = () => {
  const [keyboardState, setKeyboardState] = useState<KeyboardState>({
    isVisible: false,
    height: 0,
    animationDuration: 250,
    animationEasing: 'easeInEaseOut',
  });

  const keyboardDidShowListener = useRef<any>(null);
  const keyboardDidHideListener = useRef<any>(null);
  const keyboardWillShowListener = useRef<any>(null);
  const keyboardWillHideListener = useRef<any>(null);

  useEffect(() => {
    const handleKeyboardDidShow = (event: KeyboardEvent) => {
      setKeyboardState(prev => ({
        ...prev,
        isVisible: true,
        height: event.endCoordinates.height,
        animationDuration: event.duration || 250,
      }));
    };

    const handleKeyboardDidHide = (event: KeyboardEvent) => {
      setKeyboardState(prev => ({
        ...prev,
        isVisible: false,
        height: 0,
        animationDuration: event.duration || 250,
      }));
    };

    const handleKeyboardWillShow = (event: KeyboardEvent) => {
      setKeyboardState(prev => ({
        ...prev,
        height: event.endCoordinates.height,
        animationDuration: event.duration || 250,
      }));
    };

    const handleKeyboardWillHide = (event: KeyboardEvent) => {
      setKeyboardState(prev => ({
        ...prev,
        animationDuration: event.duration || 250,
      }));
    };

    // Set up listeners
    keyboardDidShowListener.current = Keyboard.addListener(
      'keyboardDidShow',
      handleKeyboardDidShow
    );
    keyboardDidHideListener.current = Keyboard.addListener(
      'keyboardDidHide',
      handleKeyboardDidHide
    );

    if (Platform.OS === 'ios') {
      keyboardWillShowListener.current = Keyboard.addListener(
        'keyboardWillShow',
        handleKeyboardWillShow
      );
      keyboardWillHideListener.current = Keyboard.addListener(
        'keyboardWillHide',
        handleKeyboardWillHide
      );
    }

    return () => {
      // Clean up listeners
      keyboardDidShowListener.current?.remove();
      keyboardDidHideListener.current?.remove();
      keyboardWillShowListener.current?.remove();
      keyboardWillHideListener.current?.remove();
    };
  }, []);

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const getKeyboardAvoidingViewBehavior = () => {
    return Platform.OS === 'ios' ? 'padding' : 'height';
  };

  const getKeyboardVerticalOffset = () => {
    // Adjust based on your app's header height
    return Platform.OS === 'ios' ? 0 : 0;
  };

  const getScreenHeight = () => {
    return Dimensions.get('window').height;
  };

  const getAvailableHeight = () => {
    return getScreenHeight() - keyboardState.height;
  };

  return {
    keyboardState,
    dismissKeyboard,
    getKeyboardAvoidingViewBehavior,
    getKeyboardVerticalOffset,
    getScreenHeight,
    getAvailableHeight,
  };
};

export default useKeyboardHandler;
