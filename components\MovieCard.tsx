import { View, Text, Image, Dimensions } from "react-native";

const { width: screenWidth } = Dimensions.get("window");
const cardWidth = screenWidth * 0.7; // 70% of screen width

const MovieCard = ({
  item: { title, vote_average, release_date, original_language, poster_path },
}: any) => {
  return (
    <View
      className="bg-gray-900 p-3 rounded-2xl shadow-lg"
      style={{ width: cardWidth }}
    >
      <Image
        source={{
          uri: poster_path
            ? `https://image.tmdb.org/t/p/w500/${poster_path}`
            : "no-movie.png",
        }}
        className="w-full h-[300px] rounded-xl"
        resizeMode="cover"
      />
      <View className="mt-4 space-y-3">
        <Text
          className="text-white text-xl font-rubik-semibold"
          numberOfLines={2}
        >
          {title}
        </Text>
        <View className="flex-row justify-between items-center">
          <Text className="text-gray-300 text-base font-rubik">
            ⭐ {vote_average ? vote_average.toFixed(1) : "N/A"}
          </Text>
          <Text className="text-gray-300 text-base font-rubik">
            {release_date?.split("-")[0]}
          </Text>
        </View>
        <Text className="text-gray-400 uppercase text-base font-rubik">
          {original_language}
        </Text>
      </View>
    </View>
  );
};

export default MovieCard;
