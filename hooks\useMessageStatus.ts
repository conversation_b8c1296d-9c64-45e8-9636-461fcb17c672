import { useState, useEffect, useCallback } from 'react';
import { ChatMessage, MessageDeliveryStatus } from '@/types/chat';
import { supabase, markMessageAsRead } from '@/lib/supabase';

interface UseMessageStatusProps {
  conversationId: string;
  currentUserId: string;
  messages: ChatMessage[];
}

export const useMessageStatus = ({
  conversationId,
  currentUserId,
  messages,
}: UseMessageStatusProps) => {
  const [messageStatuses, setMessageStatuses] = useState<Map<string, MessageDeliveryStatus>>(new Map());

  // Load message statuses for visible messages
  const loadMessageStatuses = useCallback(async (messageIds: string[]) => {
    if (messageIds.length === 0) return;

    try {
      const { data, error } = await supabase
        .from('message_status')
        .select('message_id, status, user_id')
        .in('message_id', messageIds);

      if (error) {
        console.error('Error loading message statuses:', error);
        return;
      }

      if (data) {
        const statusMap = new Map<string, MessageDeliveryStatus>();

        // Group statuses by message_id
        const messageStatusGroups = data.reduce((acc, status) => {
          if (!acc[status.message_id]) {
            acc[status.message_id] = [];
          }
          acc[status.message_id].push(status);
          return acc;
        }, {} as Record<string, typeof data>);

        // Calculate delivery status for each message
        Object.entries(messageStatusGroups).forEach(([messageId, statuses]) => {
          const deliveryStatus: MessageDeliveryStatus = {
            sent: true, // If we have any status, it's at least sent
            delivered: statuses.some(s => s.status === 'delivered' || s.status === 'read'),
            read: statuses.some(s => s.status === 'read'),
          };

          statusMap.set(messageId, deliveryStatus);
        });

        setMessageStatuses(prev => {
          const newMap = new Map(prev);
          statusMap.forEach((status, messageId) => {
            newMap.set(messageId, status);
          });
          return newMap;
        });
      }
    } catch (error) {
      console.error('Error loading message statuses:', error);
    }
  }, []);

  // Mark messages as read when they come into view
  const markMessagesAsRead = useCallback(async (messageIds: string[]) => {
    const unreadMessageIds = messageIds.filter(id => {
      const message = messages.find(m => m.id === id);
      return message && message.sender_id !== currentUserId;
    });

    if (unreadMessageIds.length === 0) return;

    try {
      // Mark each message as read
      await Promise.all(
        unreadMessageIds.map(messageId =>
          markMessageAsRead(messageId, currentUserId)
        )
      );

      // Update local status
      setMessageStatuses(prev => {
        const newMap = new Map(prev);
        unreadMessageIds.forEach(messageId => {
          newMap.set(messageId, {
            sent: true,
            delivered: true,
            read: true,
          });
        });
        return newMap;
      });
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  }, [currentUserId, messages]);

  // Subscribe to message status changes
  useEffect(() => {
    if (!conversationId) return;

    const subscription = supabase
      .channel(`message_status:${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'message_status',
        },
        (payload) => {
          if (payload.new) {
            const { message_id, status, user_id } = payload.new as any;
            
            // Only update if it's not our own status update
            if (user_id !== currentUserId) {
              setMessageStatuses(prev => {
                const newMap = new Map(prev);
                const currentStatus = newMap.get(message_id) || {
                  sent: true,
                  delivered: false,
                  read: false,
                };

                const updatedStatus = {
                  ...currentStatus,
                  delivered: status === 'delivered' || status === 'read' || currentStatus.delivered,
                  read: status === 'read' || currentStatus.read,
                };

                newMap.set(message_id, updatedStatus);
                return newMap;
              });
            }
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [conversationId, currentUserId]);

  // Load statuses for current messages
  useEffect(() => {
    const ownMessageIds = messages
      .filter(message => message.sender_id === currentUserId)
      .map(message => message.id);

    if (ownMessageIds.length > 0) {
      loadMessageStatuses(ownMessageIds);
    }
  }, [messages, currentUserId, loadMessageStatuses]);

  // Auto-mark visible messages as read
  useEffect(() => {
    const visibleMessageIds = messages
      .filter(message => message.sender_id !== currentUserId)
      .map(message => message.id);

    if (visibleMessageIds.length > 0) {
      // Debounce the read marking to avoid too many API calls
      const timeoutId = setTimeout(() => {
        markMessagesAsRead(visibleMessageIds);
      }, 1000);

      return () => clearTimeout(timeoutId);
    }
  }, [messages, markMessagesAsRead]);

  // Get status for a specific message
  const getMessageStatus = useCallback((messageId: string): MessageDeliveryStatus => {
    return messageStatuses.get(messageId) || {
      sent: true,
      delivered: false,
      read: false,
    };
  }, [messageStatuses]);

  // Update message status (for optimistic updates)
  const updateMessageStatus = useCallback((messageId: string, status: Partial<MessageDeliveryStatus>) => {
    setMessageStatuses(prev => {
      const newMap = new Map(prev);
      const currentStatus = newMap.get(messageId) || {
        sent: true,
        delivered: false,
        read: false,
      };

      newMap.set(messageId, { ...currentStatus, ...status });
      return newMap;
    });
  }, []);

  return {
    getMessageStatus,
    updateMessageStatus,
    markMessagesAsRead,
    messageStatuses,
  };
};

export default useMessageStatus;
