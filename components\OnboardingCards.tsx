import {
  View,
  Text,
  Image,
  Dimensions,
  StyleSheet,
  Button,
} from "react-native";
import React, { useRef, useState } from "react";
import { useVideoPlayer, VideoView } from "expo-video";
import { LinearGradient } from "expo-linear-gradient"; // Install with `expo install expo-linear-gradient`
import { useEvent } from "expo";
const { width } = Dimensions.get("window");

const OnboardingCards = ({ item }: any) => {
  return (
    <View className="flex-1 px-4 py-6">
      <View className="overflow-hidden rounded-3xl w-full relative">
        <Image
          source={item.image}
          style={{ width: width - 32, height: 300, borderRadius: 20 }}
        />

        <LinearGradient
          colors={[
            "rgba(0,0,0,0.9)",
            "rgba(0,0,0,0.7)",
            "rgba(0,0,0,0.5)",
            "rgba(0,0,0,0.3)",
            "transparent",
          ]}
          locations={[0, 0.3, 0.6, 0.8, 1]}
          style={{
            ...StyleSheet.absoluteFillObject,
            zIndex: 1,
          }}
        />
      </View>
    </View>
  );
};

export default OnboardingCards;

const styles = StyleSheet.create({
  overlayText: {
    position: "absolute",
    top: "40%",
    left: "10%",
    right: "10%",
    zIndex: 10,
    alignItems: "center",
    textAlign: "center",
  },
});
