// Extended types for the chat application
export interface ChatUser {
  id: string;
  email: string;
  username: string;
  avatar_url?: string;
  full_name?: string;
  phone?: string;
  last_seen?: string;
  is_online: boolean;
  created_at: string;
  updated_at: string;
}

export interface ChatMessage {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  message_type: 'text' | 'image' | 'file' | 'audio';
  reply_to?: string;
  created_at: string;
  updated_at: string;
  is_edited: boolean;
  sender?: ChatUser;
  status?: MessageDeliveryStatus;
  isOwn?: boolean;
}

export interface ChatConversation {
  id: string;
  name?: string;
  is_group: boolean;
  avatar_url?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  last_message?: ChatMessage;
  unread_count?: number;
  participants?: ChatUser[];
  other_participant?: ChatUser; // For 1-on-1 chats
}

export interface MessageDeliveryStatus {
  sent: boolean;
  delivered: boolean;
  read: boolean;
}

export interface TypingUser {
  id: string;
  username: string;
  avatar_url?: string;
}

export interface ChatState {
  conversations: ChatConversation[];
  currentConversation?: ChatConversation;
  messages: { [conversationId: string]: ChatMessage[] };
  typingUsers: { [conversationId: string]: TypingUser[] };
  loading: boolean;
  error?: string;
}

export interface MessageInputState {
  text: string;
  isTyping: boolean;
  replyTo?: ChatMessage;
}

export interface EmojiPickerState {
  visible: boolean;
  position: { x: number; y: number };
}

// Navigation types
export type ChatStackParamList = {
  ChatList: undefined;
  Chat: {
    conversationId: string;
    conversationName?: string;
    isGroup?: boolean;
  };
  Profile: {
    userId: string;
  };
  Settings: undefined;
};

// Component props types
export interface MessageBubbleProps {
  message: ChatMessage;
  isOwn: boolean;
  showAvatar: boolean;
  showTimestamp: boolean;
  onLongPress?: () => void;
  onReply?: () => void;
}

export interface MessageInputProps {
  onSendMessage: (text: string, replyTo?: string) => void;
  onTypingChange: (isTyping: boolean) => void;
  replyTo?: ChatMessage;
  onCancelReply?: () => void;
  placeholder?: string;
}

export interface ChatHeaderProps {
  conversation: ChatConversation;
  onBack: () => void;
  onProfilePress?: () => void;
  typingUsers?: TypingUser[];
}

export interface ConversationItemProps {
  conversation: ChatConversation;
  onPress: () => void;
  onLongPress?: () => void;
}

// Utility types
export type MessageGrouping = {
  date: string;
  messages: ChatMessage[];
};

export type ConversationFilter = 'all' | 'unread' | 'groups' | 'archived';

export type MessageReaction = {
  emoji: string;
  users: string[];
  count: number;
};

// Real-time event types
export interface RealtimeMessageEvent {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: ChatMessage;
  old?: ChatMessage;
}

export interface RealtimeTypingEvent {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: {
    conversation_id: string;
    user_id: string;
    is_typing: boolean;
    timestamp: string;
  };
  old?: {
    conversation_id: string;
    user_id: string;
    is_typing: boolean;
    timestamp: string;
  };
}

// Error types
export interface ChatError {
  code: string;
  message: string;
  details?: any;
}

// Theme types for WhatsApp-like styling
export interface ChatTheme {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    ownMessageBubble: string;
    otherMessageBubble: string;
    ownMessageText: string;
    otherMessageText: string;
    timestamp: string;
    online: string;
    offline: string;
    typing: string;
    unread: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
    full: number;
  };
  typography: {
    sizes: {
      xs: number;
      sm: number;
      md: number;
      lg: number;
      xl: number;
    };
    weights: {
      normal: string;
      medium: string;
      semibold: string;
      bold: string;
    };
  };
}

// Animation types
export interface MessageAnimation {
  type: 'slideIn' | 'fadeIn' | 'bounce';
  duration: number;
  delay?: number;
}

export interface TypingAnimation {
  dots: number;
  interval: number;
}

// Settings types
export interface ChatSettings {
  notifications: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    preview: boolean;
  };
  privacy: {
    readReceipts: boolean;
    lastSeen: boolean;
    profilePhoto: 'everyone' | 'contacts' | 'nobody';
  };
  chat: {
    fontSize: 'small' | 'medium' | 'large';
    wallpaper?: string;
    enterToSend: boolean;
  };
}
