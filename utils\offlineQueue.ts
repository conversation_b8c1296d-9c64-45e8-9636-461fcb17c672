import React from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { ChatMessage } from '@/types/chat';
import { sendMessage } from '@/lib/supabase';
import { handleError, isNetworkError } from './errorHandling';

interface QueuedMessage {
  id: string;
  conversationId: string;
  content: string;
  messageType: 'text' | 'image' | 'file' | 'audio';
  replyTo?: string;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

interface OfflineQueueState {
  messages: QueuedMessage[];
  isProcessing: boolean;
  isOnline: boolean;
}

class OfflineMessageQueue {
  private static instance: OfflineMessageQueue;
  private state: OfflineQueueState = {
    messages: [],
    isProcessing: false,
    isOnline: true,
  };
  private listeners: Array<(state: OfflineQueueState) => void> = [];
  private storageKey = '@chat_offline_queue';
  private processingInterval?: NodeJS.Timeout;

  static getInstance(): OfflineMessageQueue {
    if (!OfflineMessageQueue.instance) {
      OfflineMessageQueue.instance = new OfflineMessageQueue();
    }
    return OfflineMessageQueue.instance;
  }

  constructor() {
    this.initializeNetworkListener();
    this.loadQueueFromStorage();
  }

  private async initializeNetworkListener() {
    // Listen for network state changes
    NetInfo.addEventListener(state => {
      const wasOnline = this.state.isOnline;
      this.state.isOnline = state.isConnected ?? false;
      
      if (!wasOnline && this.state.isOnline) {
        // Just came back online, process queue
        this.processQueue();
      }
      
      this.notifyListeners();
    });

    // Get initial network state
    const netInfo = await NetInfo.fetch();
    this.state.isOnline = netInfo.isConnected ?? false;
  }

  private async loadQueueFromStorage() {
    try {
      const storedQueue = await AsyncStorage.getItem(this.storageKey);
      if (storedQueue) {
        const messages = JSON.parse(storedQueue) as QueuedMessage[];
        this.state.messages = messages;
        this.notifyListeners();
        
        // Process queue if online
        if (this.state.isOnline) {
          this.processQueue();
        }
      }
    } catch (error) {
      console.error('Failed to load offline queue:', error);
    }
  }

  private async saveQueueToStorage() {
    try {
      await AsyncStorage.setItem(this.storageKey, JSON.stringify(this.state.messages));
    } catch (error) {
      console.error('Failed to save offline queue:', error);
    }
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener({ ...this.state }));
  }

  public subscribe(listener: (state: OfflineQueueState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  public async addMessage(
    conversationId: string,
    content: string,
    messageType: 'text' | 'image' | 'file' | 'audio' = 'text',
    replyTo?: string
  ): Promise<string> {
    const messageId = `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const queuedMessage: QueuedMessage = {
      id: messageId,
      conversationId,
      content,
      messageType,
      replyTo,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 3,
    };

    this.state.messages.push(queuedMessage);
    await this.saveQueueToStorage();
    this.notifyListeners();

    // Try to send immediately if online
    if (this.state.isOnline) {
      this.processQueue();
    }

    return messageId;
  }

  public async processQueue() {
    if (this.state.isProcessing || !this.state.isOnline || this.state.messages.length === 0) {
      return;
    }

    this.state.isProcessing = true;
    this.notifyListeners();

    const messagesToProcess = [...this.state.messages];
    
    for (const queuedMessage of messagesToProcess) {
      try {
        // Attempt to send the message
        const messageData = {
          conversation_id: queuedMessage.conversationId,
          sender_id: '', // This should be set from current user context
          content: queuedMessage.content,
          message_type: queuedMessage.messageType,
          reply_to: queuedMessage.replyTo,
        };

        const { data, error } = await sendMessage(messageData);

        if (error) {
          throw error;
        }

        // Message sent successfully, remove from queue
        this.removeMessageFromQueue(queuedMessage.id);
        
      } catch (error) {
        console.error('Failed to send queued message:', error);
        
        if (isNetworkError(error)) {
          // Network error, stop processing and wait for reconnection
          break;
        } else {
          // Other error, increment retry count
          queuedMessage.retryCount++;
          
          if (queuedMessage.retryCount >= queuedMessage.maxRetries) {
            // Max retries reached, remove from queue
            this.removeMessageFromQueue(queuedMessage.id);
            handleError(error, true);
          }
        }
      }
    }

    this.state.isProcessing = false;
    await this.saveQueueToStorage();
    this.notifyListeners();
  }

  private removeMessageFromQueue(messageId: string) {
    this.state.messages = this.state.messages.filter(msg => msg.id !== messageId);
  }

  public getQueuedMessages(conversationId?: string): QueuedMessage[] {
    if (conversationId) {
      return this.state.messages.filter(msg => msg.conversationId === conversationId);
    }
    return this.state.messages;
  }

  public getQueueSize(): number {
    return this.state.messages.length;
  }

  public isOnline(): boolean {
    return this.state.isOnline;
  }

  public isProcessing(): boolean {
    return this.state.isProcessing;
  }

  public async clearQueue(): Promise<void> {
    this.state.messages = [];
    await this.saveQueueToStorage();
    this.notifyListeners();
  }

  public async retryFailedMessages(): Promise<void> {
    // Reset retry count for all messages and process queue
    this.state.messages.forEach(msg => {
      msg.retryCount = 0;
    });
    
    await this.saveQueueToStorage();
    
    if (this.state.isOnline) {
      this.processQueue();
    }
  }

  // Convert queued message to display message for UI
  public queuedMessageToDisplayMessage(queuedMessage: QueuedMessage, senderId: string): ChatMessage {
    return {
      id: queuedMessage.id,
      conversation_id: queuedMessage.conversationId,
      sender_id: senderId,
      content: queuedMessage.content,
      message_type: queuedMessage.messageType,
      created_at: new Date(queuedMessage.timestamp).toISOString(),
      updated_at: new Date(queuedMessage.timestamp).toISOString(),
      reply_to: queuedMessage.replyTo,
      isOwn: true,
      isPending: true, // Mark as pending
      retryCount: queuedMessage.retryCount,
    } as ChatMessage & { isPending: boolean; retryCount: number };
  }
}

// Export singleton instance
export const offlineQueue = OfflineMessageQueue.getInstance();

// Hook for React components
export const useOfflineQueue = () => {
  const [queueState, setQueueState] = React.useState<OfflineQueueState>({
    messages: [],
    isProcessing: false,
    isOnline: true,
  });

  React.useEffect(() => {
    const unsubscribe = offlineQueue.subscribe(setQueueState);
    return unsubscribe;
  }, []);

  return {
    queueState,
    addMessage: offlineQueue.addMessage.bind(offlineQueue),
    processQueue: offlineQueue.processQueue.bind(offlineQueue),
    getQueuedMessages: offlineQueue.getQueuedMessages.bind(offlineQueue),
    clearQueue: offlineQueue.clearQueue.bind(offlineQueue),
    retryFailedMessages: offlineQueue.retryFailedMessages.bind(offlineQueue),
    isOnline: offlineQueue.isOnline.bind(offlineQueue),
    isProcessing: offlineQueue.isProcessing.bind(offlineQueue),
  };
};

export default offlineQueue;
