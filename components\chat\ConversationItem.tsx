import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ChatConversation } from '@/types/chat';
import { chatTheme, layout } from '@/constants/theme';
import {
  getConversationName,
  getConversationAvatar,
  getLastMessagePreview,
  formatConversationTime,
  getMessageStatusIcon,
} from '@/utils/chat';
import * as Haptics from 'expo-haptics';

interface ConversationItemProps {
  conversation: ChatConversation;
  currentUserId: string;
  onPress: () => void;
  onLongPress?: () => void;
}

export const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  currentUserId,
  onPress,
  onLongPress,
}) => {
  const conversationName = getConversationName(conversation, currentUserId);
  const conversationAvatar = getConversationAvatar(conversation, currentUserId);
  const lastMessagePreview = getLastMessagePreview(conversation.last_message);
  const hasUnread = (conversation.unread_count || 0) > 0;
  const otherParticipant = conversation.participants?.find(p => p.id !== currentUserId);

  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onPress();
  };

  const handleLongPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onLongPress?.();
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      onLongPress={handleLongPress}
      className="flex-row items-center px-4 py-3"
      style={{
        backgroundColor: chatTheme.colors.surface,
        height: layout.conversationItem.height,
      }}
      activeOpacity={0.7}
    >
      {/* Avatar */}
      <View className="mr-3 relative">
        {conversationAvatar ? (
          <Image
            source={{ uri: conversationAvatar }}
            className="rounded-full"
            style={{
              width: layout.avatarSize.large,
              height: layout.avatarSize.large,
            }}
          />
        ) : (
          <View
            className="rounded-full items-center justify-center"
            style={{
              width: layout.avatarSize.large,
              height: layout.avatarSize.large,
              backgroundColor: chatTheme.colors.primary,
            }}
          >
            <Text
              className="font-bold text-white"
              style={{ fontSize: chatTheme.typography.sizes.lg }}
            >
              {conversationName.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}

        {/* Online indicator for 1-on-1 chats */}
        {!conversation.is_group && otherParticipant?.is_online && (
          <View
            className="absolute bottom-0 right-0 rounded-full border-2"
            style={{
              width: 16,
              height: 16,
              backgroundColor: chatTheme.colors.online,
              borderColor: chatTheme.colors.surface,
            }}
          />
        )}

        {/* Group indicator */}
        {conversation.is_group && (
          <View
            className="absolute bottom-0 right-0 rounded-full border-2 items-center justify-center"
            style={{
              width: 20,
              height: 20,
              backgroundColor: chatTheme.colors.secondary,
              borderColor: chatTheme.colors.surface,
            }}
          >
            <Ionicons
              name="people"
              size={10}
              color="white"
            />
          </View>
        )}
      </View>

      {/* Content */}
      <View className="flex-1 justify-center">
        {/* Name and timestamp */}
        <View className="flex-row items-center justify-between mb-1">
          <Text
            className={`flex-1 ${hasUnread ? 'font-semibold' : 'font-medium'}`}
            style={{
              color: chatTheme.colors.text,
              fontSize: chatTheme.typography.sizes.lg,
            }}
            numberOfLines={1}
          >
            {conversationName}
          </Text>

          {/* Timestamp */}
          {conversation.last_message && (
            <Text
              className={`ml-2 ${hasUnread ? 'font-medium' : ''}`}
              style={{
                color: hasUnread ? chatTheme.colors.primary : chatTheme.colors.textSecondary,
                fontSize: chatTheme.typography.sizes.sm,
              }}
            >
              {formatConversationTime(conversation.last_message.created_at)}
            </Text>
          )}
        </View>

        {/* Last message and unread count */}
        <View className="flex-row items-center justify-between">
          <View className="flex-1 flex-row items-center">
            {/* Message status for own messages */}
            {conversation.last_message?.sender_id === currentUserId && (
              <Text
                className="mr-1"
                style={{
                  color: conversation.last_message.status?.read 
                    ? '#53BDEB' 
                    : chatTheme.colors.textSecondary,
                  fontSize: chatTheme.typography.sizes.sm,
                }}
              >
                {getMessageStatusIcon(conversation.last_message)}
              </Text>
            )}

            {/* Last message preview */}
            <Text
              className={`flex-1 ${hasUnread ? 'font-medium' : ''}`}
              style={{
                color: hasUnread ? chatTheme.colors.text : chatTheme.colors.textSecondary,
                fontSize: chatTheme.typography.sizes.md,
              }}
              numberOfLines={1}
            >
              {lastMessagePreview}
            </Text>
          </View>

          {/* Unread count badge */}
          {hasUnread && (
            <View
              className="rounded-full items-center justify-center ml-2"
              style={{
                backgroundColor: chatTheme.colors.unread,
                minWidth: 20,
                height: 20,
                paddingHorizontal: 6,
              }}
            >
              <Text
                className="font-bold text-white"
                style={{ fontSize: chatTheme.typography.sizes.xs }}
              >
                {conversation.unread_count! > 99 ? '99+' : conversation.unread_count}
              </Text>
            </View>
          )}

          {/* Muted indicator */}
          {/* TODO: Add muted property to conversation type */}
          {false && (
            <View className="ml-2">
              <Ionicons
                name="volume-mute"
                size={16}
                color={chatTheme.colors.textSecondary}
              />
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ConversationItem;
