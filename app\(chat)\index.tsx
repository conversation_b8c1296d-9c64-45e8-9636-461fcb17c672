import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  StatusBar,
  TextInput,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { ConversationItem } from '@/components/chat';
import { ChatConversation, ChatUser } from '@/types/chat';
import { chatTheme, layout } from '@/constants/theme';
import { searchConversations, getTotalUnreadCount } from '@/utils/chat';
import { supabase, getConversations, getCurrentUser } from '@/lib/supabase';
import * as Haptics from 'expo-haptics';

export default function ChatListScreen() {
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [filteredConversations, setFilteredConversations] = useState<ChatConversation[]>([]);
  const [currentUser, setCurrentUser] = useState<ChatUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);

  // Load current user
  useEffect(() => {
    loadCurrentUser();
  }, []);

  // Load conversations
  useEffect(() => {
    if (currentUser) {
      loadConversations();
    }
  }, [currentUser]);

  // Filter conversations based on search
  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = searchConversations(conversations, searchQuery, currentUser?.id || '');
      setFilteredConversations(filtered);
    } else {
      setFilteredConversations(conversations);
    }
  }, [conversations, searchQuery, currentUser]);

  const loadCurrentUser = async () => {
    try {
      const user = await getCurrentUser();
      if (user) {
        // Fetch user profile from our users table
        const { data: userProfile } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();
        
        if (userProfile) {
          setCurrentUser(userProfile);
        }
      } else {
        // Redirect to sign in if no user
        router.replace('/sign-in');
      }
    } catch (error) {
      console.error('Error loading user:', error);
      Alert.alert('Error', 'Failed to load user profile');
    }
  };

  const loadConversations = async () => {
    if (!currentUser) return;

    try {
      setLoading(true);
      const { data, error } = await getConversations(currentUser.id);
      
      if (error) {
        throw error;
      }

      if (data) {
        setConversations(data);
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
      Alert.alert('Error', 'Failed to load conversations');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadConversations();
    setRefreshing(false);
  }, [currentUser]);

  const handleConversationPress = (conversation: ChatConversation) => {
    router.push({
      pathname: '/(chat)/[id]',
      params: {
        id: conversation.id,
        name: conversation.name || 'Chat',
        isGroup: conversation.is_group.toString(),
      },
    });
  };

  const handleConversationLongPress = (conversation: ChatConversation) => {
    // TODO: Show conversation options (mute, delete, archive, etc.)
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert(
      'Conversation Options',
      `Options for ${conversation.name || 'Chat'}`,
      [
        { text: 'Archive', onPress: () => {} },
        { text: 'Mute', onPress: () => {} },
        { text: 'Delete', style: 'destructive', onPress: () => {} },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleNewChat = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // TODO: Navigate to new chat screen
    Alert.alert('New Chat', 'New chat functionality coming soon!');
  };

  const handleSearch = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowSearch(!showSearch);
    if (showSearch) {
      setSearchQuery('');
    }
  };

  const handleSettings = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // TODO: Navigate to settings screen
    Alert.alert('Settings', 'Settings functionality coming soon!');
  };

  const renderConversationItem = ({ item }: { item: ChatConversation }) => (
    <ConversationItem
      conversation={item}
      currentUserId={currentUser?.id || ''}
      onPress={() => handleConversationPress(item)}
      onLongPress={() => handleConversationLongPress(item)}
    />
  );

  const renderEmptyState = () => (
    <View className="flex-1 items-center justify-center px-8">
      <Ionicons
        name="chatbubbles-outline"
        size={80}
        color={chatTheme.colors.textSecondary}
      />
      <Text
        className="text-center mt-4 mb-2"
        style={{
          color: chatTheme.colors.text,
          fontSize: chatTheme.typography.sizes.lg,
          fontWeight: chatTheme.typography.weights.semibold,
        }}
      >
        No conversations yet
      </Text>
      <Text
        className="text-center"
        style={{
          color: chatTheme.colors.textSecondary,
          fontSize: chatTheme.typography.sizes.md,
        }}
      >
        Start a new conversation to begin chatting
      </Text>
      <TouchableOpacity
        onPress={handleNewChat}
        className="mt-6 px-6 py-3 rounded-full"
        style={{ backgroundColor: chatTheme.colors.primary }}
      >
        <Text
          className="font-semibold text-white"
          style={{ fontSize: chatTheme.typography.sizes.md }}
        >
          Start New Chat
        </Text>
      </TouchableOpacity>
    </View>
  );

  const totalUnreadCount = getTotalUnreadCount(filteredConversations);

  return (
    <View className="flex-1" style={{ backgroundColor: chatTheme.colors.background }}>
      <StatusBar barStyle="light-content" backgroundColor={chatTheme.colors.surface} />
      
      {/* Header */}
      <View
        className="px-4 pt-2 pb-2"
        style={{
          backgroundColor: chatTheme.colors.surface,
          paddingTop: (StatusBar.currentHeight || 0) + 8,
        }}
      >
        <View className="flex-row items-center justify-between">
          <Text
            className="font-bold"
            style={{
              color: chatTheme.colors.text,
              fontSize: chatTheme.typography.sizes.xl,
            }}
          >
            Chats
            {totalUnreadCount > 0 && (
              <Text
                style={{
                  color: chatTheme.colors.primary,
                  fontSize: chatTheme.typography.sizes.lg,
                }}
              >
                {' '}({totalUnreadCount})
              </Text>
            )}
          </Text>

          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={handleSearch}
              className="p-2 mr-2"
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons
                name={showSearch ? "close" : "search"}
                size={24}
                color={chatTheme.colors.text}
              />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleSettings}
              className="p-2"
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons
                name="settings-outline"
                size={24}
                color={chatTheme.colors.text}
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search bar */}
        {showSearch && (
          <View
            className="mt-3 px-4 py-2 rounded-full flex-row items-center"
            style={{ backgroundColor: chatTheme.colors.background }}
          >
            <Ionicons
              name="search"
              size={20}
              color={chatTheme.colors.textSecondary}
              style={{ marginRight: 8 }}
            />
            <TextInput
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search conversations..."
              placeholderTextColor={chatTheme.colors.textSecondary}
              className="flex-1"
              style={{
                color: chatTheme.colors.text,
                fontSize: chatTheme.typography.sizes.md,
              }}
              autoFocus
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                onPress={() => setSearchQuery('')}
                className="p-1"
              >
                <Ionicons
                  name="close-circle"
                  size={20}
                  color={chatTheme.colors.textSecondary}
                />
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>

      {/* Conversations list */}
      <FlatList
        data={filteredConversations}
        renderItem={renderConversationItem}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={chatTheme.colors.primary}
            colors={[chatTheme.colors.primary]}
          />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
        contentContainerStyle={
          filteredConversations.length === 0 ? { flex: 1 } : undefined
        }
        showsVerticalScrollIndicator={false}
      />

      {/* Floating action button */}
      <TouchableOpacity
        onPress={handleNewChat}
        className="absolute bottom-6 right-6 rounded-full items-center justify-center shadow-lg"
        style={{
          backgroundColor: chatTheme.colors.primary,
          width: 56,
          height: 56,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 4,
        }}
      >
        <Ionicons
          name="add"
          size={28}
          color="white"
        />
      </TouchableOpacity>
    </View>
  );
}
