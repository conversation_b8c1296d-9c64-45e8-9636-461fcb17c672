{"name": "restate-react-native", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@supabase/supabase-js": "^2.53.0", "axios": "^1.7.9", "date-fns": "^4.1.0", "expo": "~52.0.28", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.2", "expo-constants": "~17.0.4", "expo-font": "~13.0.3", "expo-haptics": "~14.0.1", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-router": "~4.0.17", "expo-splash-screen": "~0.29.21", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.7", "expo-video": "~2.0.5", "expo-web-browser": "~14.0.2", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.6", "react-native-animatable": "^1.4.0", "react-native-appwrite": "^0.6.0", "react-native-emoji-picker": "^0.2.2", "react-native-gesture-handler": "~2.20.2", "react-native-image-picker": "^8.2.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.3.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.3", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}