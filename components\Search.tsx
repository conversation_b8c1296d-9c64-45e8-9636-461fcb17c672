import { View, Text, Image, TextInput } from "react-native";
import React from "react";
import images from "@/constants/images";

const Search = ({ searchTerm, setSearchTerm }: any) => {
  return (
    <View className="bg-primary-100 flex-row rounded-xl shadow-xl items-center">
      <View className="flex-row flex-1 items-center bg-primary-100 px-4 py-3 rounded-xl">
        <Image
          source={images.search}
          className="w-6 h-6 mr-3 opacity-60"
          resizeMode="contain"
        />
        <TextInput
          placeholder="Search your favourite movie..."
          placeholderTextColor="#6B7280"
          className="text-gray-400 text-base flex-1 font-medium font-rubik"
          value={searchTerm}
          onChange={(event) => setSearchTerm(event.nativeEvent.text)}
        />
      </View>
    </View>
  );
};

export default Search;
