import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  ActivityIndicator,
  FlatList,
  Dimensions,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Search from "@/components/Search";
import MovieCard from "@/components/MovieCard";
import * as Animatable from "react-native-animatable";
import { getTrendingMovies, updateSearchCount } from "@/lib/appwrite";
import { Models } from "react-native-appwrite";
import TrendingMovieCard from "@/components/TrendingMovieCard";

const { width: screenWidth } = Dimensions.get("window");
const cardWidth = screenWidth * 0.7; // Card occupies 70% of the screen
const cardSpacing = (screenWidth - cardWidth) / 2; // Equal spacing on both sides

const Explore = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [movies, setMovies] = useState<{ id: number }[]>([]);
  const [trensdingMovies, setTrendingMovies] = useState<Models.Document[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeIndex, setActiveIndex] = useState(0);

  // Debounce Effect
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm]);

  const fetchMovies = async (query = "") => {
    try {
      setLoading(true);
      const response = await fetch(
        query
          ? `http://192.168.31.241:8081/movie?query=${encodeURIComponent(
              query
            )}`
          : "http://192.168.31.241:8081/movie"
      );
      const data = await response.json();
      setMovies(data.results || []);

      if (query && data.results.length > 0) {
        updateSearchCount(query, data.results[0]);
      }
    } catch (error) {
      setError("Failed to fetch movies. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const loadTrendingMovies = async () => {
    try {
      const movies = await getTrendingMovies();
      setTrendingMovies(movies);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    fetchMovies(debouncedSearchTerm);
  }, [debouncedSearchTerm]);

  useEffect(() => {
    loadTrendingMovies();
  }, []);

  
  return (
    <SafeAreaView className="h-full bg-black">
      <Search searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
      {trensdingMovies.length > 0 && (
        <View className="px-4">
          <Text className="text-white text-lg font-rubik-semibold mt-4">
            Trending Movies
          </Text>
          <FlatList
            data={trensdingMovies}
            keyExtractor={(item) => item.$id}
            renderItem={({ item, index }) => (
              <TrendingMovieCard item={item} index={index} />
            )}
            className="mt-4"
            horizontal
            showsHorizontalScrollIndicator={false}
            snapToInterval={cardWidth + cardSpacing} // Ensures snapping aligns with card width and spacing
            decelerationRate="fast"
            contentContainerStyle={{
              paddingRight: cardSpacing, // Add padding to the end for better scrolling
            }}
          />
        </View>
      )}
      {loading ? (
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#fff" />
        </View>
      ) : error ? (
        <Text className="text-red-500 text-center mt-4">{error}</Text>
      ) : (
        <View className="flex-1 mt-20">
          <View className="px-4">
            <Text className="text-white text-lg font-rubik-semibold mt-4">
              Popular Movies
            </Text>
            <FlatList
              data={movies}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item, index }) => (
                <Animatable.View
                  animation={activeIndex === index ? "pulse" : undefined}
                  duration={500}
                  style={{
                    transform: [{ scale: activeIndex === index ? 1 : 0.9 }],
                    opacity: activeIndex === index ? 1 : 0.7,
                    marginTop: 20,
                  }}
                >
                  <MovieCard item={item} />
                </Animatable.View>
              )}
              className="mt-8"
              horizontal
              showsHorizontalScrollIndicator={false}
              snapToInterval={cardWidth} // Ensures snapping aligns with card width
              decelerationRate="fast"
              scrollEventThrottle={16}
              
            />
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export default Explore;
