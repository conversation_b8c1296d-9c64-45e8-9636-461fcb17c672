import { useEffect, useRef, useCallback } from 'react';
import { ChatMessage, TypingUser, RealtimeMessageEvent, RealtimeTypingEvent } from '@/types/chat';
import { subscribeToMessages, subscribeToTyping, markMessageAsRead } from '@/lib/supabase';

interface UseRealtimeMessagesProps {
  conversationId: string;
  currentUserId: string;
  onNewMessage: (message: ChatMessage) => void;
  onMessageUpdate: (message: ChatMessage) => void;
  onTypingChange: (typingUsers: TypingUser[]) => void;
}

export const useRealtimeMessages = ({
  conversationId,
  currentUserId,
  onNewMessage,
  onMessageUpdate,
  onTypingChange,
}: UseRealtimeMessagesProps) => {
  const messageSubscriptionRef = useRef<any>(null);
  const typingSubscriptionRef = useRef<any>(null);
  const typingUsersRef = useRef<TypingUser[]>([]);
  const typingTimeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Handle new messages
  const handleMessageEvent = useCallback((payload: any) => {
    const { eventType, new: newMessage, old: oldMessage } = payload;

    if (eventType === 'INSERT' && newMessage) {
      const message: ChatMessage = {
        ...newMessage,
        isOwn: newMessage.sender_id === currentUserId,
      };

      onNewMessage(message);

      // Auto-mark as read if not own message
      if (message.sender_id !== currentUserId) {
        markMessageAsRead(message.id, currentUserId).catch(console.error);
      }
    } else if (eventType === 'UPDATE' && newMessage) {
      const message: ChatMessage = {
        ...newMessage,
        isOwn: newMessage.sender_id === currentUserId,
      };

      onMessageUpdate(message);
    }
  }, [conversationId, currentUserId, onNewMessage, onMessageUpdate]);

  // Handle typing indicators
  const handleTypingEvent = useCallback((payload: any) => {
    const { eventType, new: newTyping, old: oldTyping } = payload;

    if (newTyping && newTyping.user_id !== currentUserId) {
      const { user_id, is_typing } = newTyping;

      // Clear existing timeout for this user
      const existingTimeout = typingTimeoutsRef.current.get(user_id);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
        typingTimeoutsRef.current.delete(user_id);
      }

      if (is_typing) {
        // Add user to typing list if not already there
        const currentTyping = typingUsersRef.current;
        const userExists = currentTyping.some(user => user.id === user_id);
        
        if (!userExists) {
          // You would need to fetch user details or have them available
          // For now, we'll create a basic typing user object
          const typingUser: TypingUser = {
            id: user_id,
            username: 'User', // This should be fetched from conversation participants
            avatar_url: undefined,
          };

          const updatedTyping = [...currentTyping, typingUser];
          typingUsersRef.current = updatedTyping;
          onTypingChange(updatedTyping);
        }

        // Set timeout to remove user from typing list
        const timeout = setTimeout(() => {
          const filtered = typingUsersRef.current.filter(user => user.id !== user_id);
          typingUsersRef.current = filtered;
          onTypingChange(filtered);
          typingTimeoutsRef.current.delete(user_id);
        }, 3000); // Remove after 3 seconds

        typingTimeoutsRef.current.set(user_id, timeout);
      } else {
        // Remove user from typing list immediately
        const filtered = typingUsersRef.current.filter(user => user.id !== user_id);
        typingUsersRef.current = filtered;
        onTypingChange(filtered);
      }
    }
  }, [currentUserId, onTypingChange]);

  // Subscribe to real-time updates
  useEffect(() => {
    if (!conversationId || !currentUserId) return;

    // Subscribe to messages
    messageSubscriptionRef.current = subscribeToMessages(
      conversationId,
      handleMessageEvent
    );

    // Subscribe to typing indicators
    typingSubscriptionRef.current = subscribeToTyping(
      conversationId,
      handleTypingEvent
    );

    return () => {
      // Cleanup subscriptions
      if (messageSubscriptionRef.current) {
        messageSubscriptionRef.current.unsubscribe();
      }
      if (typingSubscriptionRef.current) {
        typingSubscriptionRef.current.unsubscribe();
      }

      // Clear all typing timeouts
      typingTimeoutsRef.current.forEach(timeout => clearTimeout(timeout));
      typingTimeoutsRef.current.clear();
    };
  }, [conversationId, currentUserId, handleMessageEvent, handleTypingEvent]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear all typing timeouts
      typingTimeoutsRef.current.forEach(timeout => clearTimeout(timeout));
      typingTimeoutsRef.current.clear();
    };
  }, []);

  return {
    // You can return any additional utilities here if needed
    cleanup: () => {
      if (messageSubscriptionRef.current) {
        messageSubscriptionRef.current.unsubscribe();
      }
      if (typingSubscriptionRef.current) {
        typingSubscriptionRef.current.unsubscribe();
      }
      typingTimeoutsRef.current.forEach(timeout => clearTimeout(timeout));
      typingTimeoutsRef.current.clear();
    },
  };
};

export default useRealtimeMessages;
