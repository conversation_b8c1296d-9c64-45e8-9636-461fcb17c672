const API_OPTIONS = {
  method: "GET",
  headers: {
    Accept: "application/json",
    "Content-Type": "application/json",
    Authorization:
      "Bearer eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJhMmQ0NmY2NzdlNGZiNjgwMjg5ZWM2NGQ3MThlNjc2OSIsIm5iZiI6MTczNzg3Mzg5MC44MzEsInN1YiI6IjY3OTVkOWUyOWEzMGE4NWIyNzIzYWM4MyIsInNjb3BlcyI6WyJhcGlfcmVhZCJdLCJ2ZXJzaW9uIjoxfQ.Ntln3o9O0RUFC59O2yCekG0q-DufSVSWHK7p8d4AgnM",
  },
};

const API_BASE_URL = "https://api.themoviedb.org/3";

export async function GET(request: Request) {
  try {
    const url = new URL(request.url); // Get the full request URL
    const query = url.searchParams.get("query"); // Extract the query parameter

    const endpoint = query
      ? `${API_BASE_URL}/search/movie?query=${encodeURIComponent(query)}`
      : `${API_BASE_URL}/movie/popular`;

    console.log("Requesting:", endpoint);

    const response = await fetch(endpoint, API_OPTIONS);
    console.log("Response Status:", response.status);

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.status_message || "Failed to fetch movies");
    }

    return Response.json(data);
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return Response.json({ error: errorMessage }, { status: 500 });
  }
}
