import {
  Account,
  Avatars,
  Client,
  Databases,
  ID,
  OAuthProvider,
  Query,
} from "react-native-appwrite";
import * as Linking from "expo-linking";
import { openAuthSessionAsync } from "expo-web-browser";

export const config = {
  platform: "com.habib.restate",
  endpoint: process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT,
  projectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID,
  collectionId: process.env.EXPO_PUBLIC_APPWRITE_COLLECTION_ID,
  databaseId: process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID,
};

export const client = new Client();

client
  .setEndpoint(config.endpoint!)
  .setProject(config.projectId!)
  .setPlatform(config.platform!);

export const avatar = new Avatars(client);
export const account = new Account(client);
const database = new Databases(client);

export const updateSearchCount = async (searchTerm: any, movie: any) => {
  try {
    const result = await database.listDocuments(
      config.databaseId!,
      config.collectionId!,
      [Query.equal("searchTerm", searchTerm)]
    );

    if (result.documents.length > 0) {
      const doc = result.documents[0];
      await database.updateDocument(
        config.databaseId!,
        config.collectionId!,
        doc.$id,
        {
          count: doc.count + 1,
        }
      );
    } else {
      database.createDocument(
        config.databaseId!,
        config.collectionId!,
        ID.unique(),
        {
          searchTerm,
          count: 1,
          movie_id: movie.id,
          poster_url: `https://image.tmdb.org/t/p/w500/${movie.poster_path}`,
        }
      );
    }
  } catch (error) {
    console.error(error);
  }
};

export const getTrendingMovies = async () => {
  try {
    const result = await database.listDocuments(
      config.databaseId!,
      config.collectionId!,
      [Query.limit(10), Query.orderDesc("count")]
    );

    return result.documents;
  } catch (error) {
    console.error(error);
    return [];
  }
};

export async function login() {
  try {
    const redirectUri = Linking.createURL("/");
    const response = await account.createOAuth2Token(
      OAuthProvider.Google,
      redirectUri
    );

    if (!response) {
      console.error("No response from OAuth token creation");
      throw new Error("OAuth token creation failed");
    }

    const browserResult = await openAuthSessionAsync(
      response.toString(),
      redirectUri
    ).catch((error) => {
      console.error("Browser session failed:", error);
      throw error;
    });

    if (browserResult.type !== "success") {
      console.error("Browser result type:", browserResult.type);
      throw new Error(`Auth session failed: ${browserResult.type}`);
    }

    const url = new URL(browserResult.url);
    const secret = url.searchParams.get("secret")?.toString();
    const userId = url.searchParams.get("userId")?.toString();

    if (!secret || !userId) {
      console.error("Missing credentials:", {
        secret: !!secret,
        userId: !!userId,
      });
      throw new Error("Missing authentication credentials");
    }

    const session = await account
      .createSession(userId, secret)
      .catch((error) => {
        console.error("Session creation failed:", error);
        throw error;
      });

    if (!session) {
      console.error("No session created");
      throw new Error("Session creation failed");
    }
    return true;
  } catch (error) {
    console.error("Login process failed:", error);
    return false;
  }
}

export async function logout() {
  try {
    await account.deleteSession("current");
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
}

export async function getCurrentUser() {
  try {
    // Check if we have an active session first
    try {
      await account.getSession("current");
    } catch {
      return null;
    }

    // Only proceed to get user data if we have a valid session
    const user = await account.get();
    if (user.$id) {
      const userAvatar = await avatar.getInitials(user.name);
      return { ...user, avatar: userAvatar.toString() };
    }
    return user;
  } catch (error) {
    if (error instanceof Error && error.toString().includes("missing scope")) {
      return null;
    }
    console.error(error);
    return null;
  }
}
