# WhatsApp-like Chat Application

A comprehensive React Native chat application built with Expo, Supabase, and TypeScript, featuring real-time messaging, WhatsApp-style UI, and robust offline support.

## Features

### 🚀 Core Features
- **Real-time messaging** with Supabase subscriptions
- **WhatsApp-style UI** with dark theme
- **Message status indicators** (sent, delivered, read)
- **Typing indicators** with real-time updates
- **Offline message queuing** with automatic retry
- **Message formatting** (bold, italic, code, links)
- **Emoji picker** with categories and search
- **Reply to messages** functionality
- **Message editing and deletion** (time-limited)
- **Haptic feedback** throughout the app
- **Smooth animations** and transitions

### 📱 User Interface
- **Chat list screen** with recent conversations
- **Individual chat screens** with message history
- **Message bubbles** with sender/receiver styling
- **Date separators** for message grouping
- **Avatar display** with online indicators
- **Search functionality** for conversations
- **Pull-to-refresh** and infinite scrolling
- **Keyboard handling** and input optimization

### 🔧 Technical Features
- **TypeScript** for type safety
- **Supabase** for backend and real-time features
- **Row Level Security (RLS)** for data protection
- **Performance optimization** for large message lists
- **Error handling** with user-friendly messages
- **Network status monitoring**
- **Automatic reconnection** and message retry

## Setup Instructions

### 1. Prerequisites
- Node.js (v16 or higher)
- Expo CLI (`npm install -g @expo/cli`)
- Supabase account

### 2. Install Dependencies
```bash
npm install
```

### 3. Supabase Setup

#### Create a new Supabase project
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Note your project URL and anon key

#### Set up the database
1. Run the SQL schema from `supabase-schema.sql` in your Supabase SQL editor
2. Enable Row Level Security on all tables
3. The schema includes:
   - Users table with profiles
   - Conversations and participants
   - Messages with status tracking
   - Typing indicators
   - Proper RLS policies

#### Configure environment variables
Create a `.env` file in the root directory:
```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Run the Application
```bash
# Start the development server
npm start

# Run on iOS simulator
npm run ios

# Run on Android emulator
npm run android
```

## Project Structure

```
├── app/                    # Expo Router pages
│   ├── (chat)/            # Chat-related screens
│   │   ├── index.tsx      # Chat list screen
│   │   ├── [id].tsx       # Individual chat screen
│   │   └── _layout.tsx    # Chat layout
│   └── _layout.tsx        # Root layout
├── components/
│   └── chat/              # Chat components
│       ├── MessageBubble.tsx
│       ├── MessageInput.tsx
│       ├── ChatHeader.tsx
│       ├── TypingIndicator.tsx
│       ├── ConversationItem.tsx
│       ├── EmojiPicker.tsx
│       └── index.ts
├── lib/
│   └── supabase.ts        # Supabase client and functions
├── types/
│   └── chat.ts            # TypeScript type definitions
├── utils/
│   ├── chat.ts            # Chat utility functions
│   ├── errorHandling.ts   # Error handling utilities
│   └── offlineQueue.ts    # Offline message queue
├── hooks/
│   ├── useRealtimeMessages.ts
│   ├── useMessageStatus.ts
│   ├── useKeyboardHandler.ts
│   └── useMessageListOptimization.ts
├── contexts/
│   └── ChatContext.tsx    # Global chat state management
├── constants/
│   └── theme.ts           # Theme and styling constants
└── __tests__/
    └── chat.test.ts       # Test suite
```

## Key Components

### MessageBubble
Displays individual messages with WhatsApp-style bubbles, supporting:
- Text, image, file, and audio messages
- Reply indicators and edit status
- Timestamp and delivery status
- Long press actions

### MessageInput
Multi-line text input with:
- Auto-expanding height
- Emoji picker integration
- Send button animation
- Typing indicator management
- Reply functionality

### ChatHeader
WhatsApp-style header showing:
- Conversation name and avatar
- Online status and typing indicators
- Back navigation and action buttons

### ConversationItem
Chat list item displaying:
- Conversation name and avatar
- Last message preview
- Timestamp and unread count
- Online indicators

## Real-time Features

### Message Subscriptions
```typescript
// Subscribe to new messages in a conversation
const subscription = subscribeToMessages(conversationId, (payload) => {
  if (payload.eventType === 'INSERT') {
    // Handle new message
  }
});
```

### Typing Indicators
```typescript
// Update typing status
await updateTypingStatus(conversationId, userId, true);

// Subscribe to typing events
const typingSubscription = subscribeToTyping(conversationId, (payload) => {
  // Handle typing status changes
});
```

## Offline Support

The app includes a robust offline message queue that:
- Stores messages locally when offline
- Automatically retries failed sends
- Processes queue when connection is restored
- Provides visual feedback for pending messages

```typescript
// Add message to offline queue
const messageId = await offlineQueue.addMessage(
  conversationId,
  content,
  'text'
);
```

## Error Handling

Comprehensive error handling with:
- User-friendly error messages
- Automatic retry mechanisms
- Network status monitoring
- Graceful degradation

```typescript
// Handle errors with context
try {
  await sendMessage(messageData);
} catch (error) {
  const appError = handleError(error);
  // Error is automatically displayed to user
}
```

## Performance Optimizations

- **Virtualized message lists** for large conversations
- **Message grouping** by date and sender
- **Optimistic updates** for immediate feedback
- **Lazy loading** of older messages
- **Image optimization** and caching
- **Debounced typing indicators**

## Testing

Run the test suite:
```bash
npm test
```

The test suite covers:
- Utility functions
- Message formatting
- Date/time handling
- Error scenarios
- Offline queue functionality

## Customization

### Theme
Modify `constants/theme.ts` to customize:
- Colors and typography
- Message bubble styles
- Animation configurations
- Layout constants

### Message Types
Extend message types in `types/chat.ts` and update:
- Database schema
- Message rendering logic
- Input handling

### Real-time Events
Add new real-time features by:
- Extending Supabase subscriptions
- Adding new event handlers
- Updating UI components

## Security

The app implements several security measures:
- **Row Level Security (RLS)** policies in Supabase
- **JWT token validation** for API calls
- **Input sanitization** for messages
- **File type validation** for uploads
- **Rate limiting** considerations

## Deployment

### Expo Build
```bash
# Build for production
expo build:android
expo build:ios
```

### Supabase Production
1. Set up production Supabase project
2. Update environment variables
3. Configure proper RLS policies
4. Set up database backups

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the test suite for examples
3. Open an issue on GitHub

---

Built with ❤️ using React Native, Expo, and Supabase.
