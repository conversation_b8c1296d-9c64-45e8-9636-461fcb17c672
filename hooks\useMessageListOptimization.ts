import { useMemo, useCallback, useRef, useState } from 'react';
import { ChatMessage } from '@/types/chat';
import { groupMessagesByDate } from '@/utils/chat';

interface UseMessageListOptimizationProps {
  messages: ChatMessage[];
  windowSize?: number;
  initialNumToRender?: number;
  maxToRenderPerBatch?: number;
}

interface OptimizedMessage extends ChatMessage {
  key: string;
  showDateSeparator?: boolean;
  dateGroup?: string;
}

export const useMessageListOptimization = ({
  messages,
  windowSize = 10,
  initialNumToRender = 20,
  maxToRenderPerBatch = 10,
}: UseMessageListOptimizationProps) => {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: initialNumToRender });
  const flatListRef = useRef<any>(null);

  // Memoize processed messages with date separators
  const processedMessages = useMemo(() => {
    const grouped = groupMessagesByDate(messages);
    const processed: OptimizedMessage[] = [];

    Object.entries(grouped).forEach(([date, dateMessages]) => {
      // Add date separator
      processed.push({
        id: `date-separator-${date}`,
        conversation_id: '',
        sender_id: '',
        content: date,
        message_type: 'system',
        created_at: dateMessages[0].created_at,
        updated_at: dateMessages[0].created_at,
        key: `date-${date}`,
        showDateSeparator: true,
        dateGroup: date,
      } as OptimizedMessage);

      // Add messages for this date
      dateMessages.forEach((message, index) => {
        processed.push({
          ...message,
          key: `message-${message.id}`,
          dateGroup: date,
        });
      });
    });

    return processed;
  }, [messages]);

  // Memoize visible messages based on current range
  const visibleMessages = useMemo(() => {
    return processedMessages.slice(
      Math.max(0, visibleRange.start - windowSize),
      Math.min(processedMessages.length, visibleRange.end + windowSize)
    );
  }, [processedMessages, visibleRange, windowSize]);

  // Handle scroll to update visible range
  const handleScroll = useCallback((event: any) => {
    const { contentOffset, layoutMeasurement, contentSize } = event.nativeEvent;
    const scrollPosition = contentOffset.y;
    const viewportHeight = layoutMeasurement.height;
    const contentHeight = contentSize.height;

    // Estimate item height (you might want to make this more accurate)
    const estimatedItemHeight = 80;
    const estimatedStartIndex = Math.floor(scrollPosition / estimatedItemHeight);
    const estimatedEndIndex = Math.ceil(
      (scrollPosition + viewportHeight) / estimatedItemHeight
    );

    setVisibleRange({
      start: Math.max(0, estimatedStartIndex),
      end: Math.min(processedMessages.length, estimatedEndIndex),
    });
  }, [processedMessages.length]);

  // Optimized scroll to bottom
  const scrollToBottom = useCallback((animated = true) => {
    if (flatListRef.current && processedMessages.length > 0) {
      flatListRef.current.scrollToEnd({ animated });
    }
  }, [processedMessages.length]);

  // Optimized scroll to specific message
  const scrollToMessage = useCallback((messageId: string, animated = true) => {
    const messageIndex = processedMessages.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1 && flatListRef.current) {
      flatListRef.current.scrollToIndex({
        index: messageIndex,
        animated,
        viewPosition: 0.5, // Center the message
      });
    }
  }, [processedMessages]);

  // Get item layout for better performance
  const getItemLayout = useCallback((data: any, index: number) => {
    const item = data[index];
    let height = 80; // Default message height

    if (item?.showDateSeparator) {
      height = 40; // Date separator height
    } else if (item?.message_type === 'image') {
      height = 200; // Image message height
    } else if (item?.message_type === 'file') {
      height = 100; // File message height
    }

    return {
      length: height,
      offset: height * index,
      index,
    };
  }, []);

  // Key extractor for better performance
  const keyExtractor = useCallback((item: OptimizedMessage) => item.key, []);

  // Handle viewability changes for read receipts
  const handleViewableItemsChanged = useCallback(({ viewableItems }: any) => {
    // Mark messages as viewed for read receipts
    const viewableMessageIds = viewableItems
      .filter((item: any) => item.item && !item.item.showDateSeparator)
      .map((item: any) => item.item.id);

    // You can emit an event or call a callback here to mark messages as read
    // onMessagesViewed?.(viewableMessageIds);
  }, []);

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 50,
    minimumViewTime: 1000, // 1 second
  };

  // Load more messages when reaching the top
  const handleLoadMore = useCallback(() => {
    // This would typically trigger loading older messages
    // onLoadMoreMessages?.();
  }, []);

  // Check if we're at the bottom of the list
  const isAtBottom = useCallback((event: any) => {
    const { contentOffset, layoutMeasurement, contentSize } = event.nativeEvent;
    const paddingToBottom = 20;
    return (
      contentOffset.y >= contentSize.height - layoutMeasurement.height - paddingToBottom
    );
  }, []);

  return {
    processedMessages,
    visibleMessages,
    flatListRef,
    handleScroll,
    scrollToBottom,
    scrollToMessage,
    getItemLayout,
    keyExtractor,
    handleViewableItemsChanged,
    viewabilityConfig,
    handleLoadMore,
    isAtBottom,
    initialNumToRender,
    maxToRenderPerBatch,
    windowSize,
  };
};

export default useMessageListOptimization;
