export default {
  expo: {
    name: "WhatsApp Chat",
    slug: "whatsapp-chat",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "whatsapp-chat",
    userInterfaceStyle: "automatic",
    splash: {
      image: "./assets/images/splash.png",
      resizeMode: "contain",
      backgroundColor: "#0B141B"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.yourcompany.whatsappchat",
      infoPlist: {
        NSCameraUsageDescription: "This app needs access to camera to take photos for messages.",
        NSMicrophoneUsageDescription: "This app needs access to microphone to record voice messages.",
        NSPhotoLibraryUsageDescription: "This app needs access to photo library to send images."
      }
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#0B141B"
      },
      package: "com.yourcompany.whatsappchat",
      permissions: [
        "android.permission.CAMERA",
        "android.permission.RECORD_AUDIO",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.WRITE_EXTERNAL_STORAGE",
        "android.permission.VIBRATE",
        "android.permission.ACCESS_NETWORK_STATE"
      ]
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/favicon.png"
    },
    plugins: [
      "expo-router",
      [
        "expo-haptics",
        {
          ios: {
            NSHapticsUsageDescription: "This app uses haptics to provide tactile feedback for user interactions."
          }
        }
      ],
      [
        "expo-image-picker",
        {
          photosPermission: "The app accesses your photos to let you share them in messages.",
          cameraPermission: "The app accesses your camera to let you take photos for messages."
        }
      ],
      [
        "expo-av",
        {
          microphonePermission: "Allow $(PRODUCT_NAME) to access your microphone to record voice messages."
        }
      ]
    ],
    experiments: {
      typedRoutes: true
    },
    extra: {
      router: {
        origin: false
      },
      eas: {
        projectId: "your-eas-project-id"
      }
    }
  }
};
