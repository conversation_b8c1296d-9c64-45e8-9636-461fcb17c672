import OnboardingCards from "@/components/OnboardingCards";
import { onboarding } from "@/constants/data";
import images from "@/constants/images";
import { Link } from "expo-router";
import {
  FlatList,
  Image,
  ImageBackground,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function Index() {
  return (
    <SafeAreaView className="h-full">
      <ImageBackground
        source={images.bg}
        resizeMode="cover"
        className="flex-1 relative"
      >
        <View className="absolute inset-0 justify-center items-center px-4 z-10">
          <Image
            source={images.heroImg}
            className="w-full h-[400px] z-20"
            resizeMode="contain"
          />
          <Text className="text-primary-300 text-3xl font-rubik-bold z-20">
            Lights. Camera. Action!
          </Text>
          <Text className="text-white font-rubik text-center text-lg mt-4 z-20">
            Step into a curated world of movies and TV shows tailored for you.
          </Text>
          <Link href="/explore" className="w-full bg-primary-300 rounded-full mt-4 py-2">
            <Text className="text-white font-rubik text-center text-lg mt-4 z-20">
              Explore
            </Text>
          </Link>
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
}
