{"expo": {"name": "uber-react-native", "slug": "uber-react-native", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#ffffff"}, "config": {"usesCleartextTraffic": true}}, "web": {"bundler": "metro", "output": "server", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "resizeMode": "cover", "backgroundColor": "#ffffff", "enableFullScreenImage_legacy": true}], ["expo-font", {"fonts": ["./assets/fonts/Rubik-Regular.ttf", "./assets/fonts/Rubik-Medium.ttf", "./assets/fonts/Rubik-Bold.ttf", "./assets/fonts/Rubik-Light.ttf", "./assets/fonts/Rubik-SemiBold.ttf", "./assets/fonts/Rubik-ExtraBold.ttf"]}], ["expo-video", {"supportsBackgroundPlayback": true, "supportsPictureInPicture": true}], ["expo-build-properties", {"android": {"usesCleartextTraffic": true}, "ios": {"usesCleartextTraffic": true}}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}], ["expo-av", {"microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone."}]], "experiments": {"typedRoutes": true}}}