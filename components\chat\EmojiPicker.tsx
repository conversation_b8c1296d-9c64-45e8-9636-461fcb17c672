import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  Dimensions,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { chatTheme, emojiConfig } from '@/constants/theme';
import * as Haptics from 'expo-haptics';

interface EmojiPickerProps {
  visible: boolean;
  onClose: () => void;
  onEmojiSelect: (emoji: string) => void;
}

// Common emojis organized by category
const emojiCategories = {
  smileys_people: {
    name: 'Smileys & People',
    icon: '😀',
    emojis: [
      '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃',
      '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙',
      '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔',
      '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥',
      '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧',
      '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '😎', '🤓', '🧐',
    ],
  },
  animals_nature: {
    name: 'Animals & Nature',
    icon: '🐶',
    emojis: [
      '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
      '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒',
      '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇',
      '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜',
      '🦟', '🦗', '🕷', '🕸', '🦂', '🐢', '🐍', '🦎', '🦖', '🦕',
      '🐙', '🦑', '🦐', '🦞', '🦀', '🐡', '🐠', '🐟', '🐬', '🐳',
    ],
  },
  food_drink: {
    name: 'Food & Drink',
    icon: '🍎',
    emojis: [
      '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈',
      '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦',
      '🥬', '🥒', '🌶', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔',
      '🍠', '🥐', '🥖', '🍞', '🥨', '🥯', '🧀', '🥚', '🍳', '🧈',
      '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🦴', '🌭', '🍔', '🍟',
      '🍕', '🥪', '🥙', '🧆', '🌮', '🌯', '🫔', '🥗', '🥘', '🫕',
    ],
  },
  activities: {
    name: 'Activities',
    icon: '⚽',
    emojis: [
      '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
      '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳',
      '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸',
      '🥌', '🎿', '⛷', '🏂', '🪂', '🏋', '🤼', '🤸', '⛹', '🤺',
      '🏇', '🧘', '🏄', '🏊', '🤽', '🚣', '🧗', '🚵', '🚴', '🏆',
      '🥇', '🥈', '🥉', '🏅', '🎖', '🏵', '🎗', '🎫', '🎟', '🎪',
    ],
  },
  travel_places: {
    name: 'Travel & Places',
    icon: '🚗',
    emojis: [
      '🚗', '🚕', '🚙', '🚌', '🚎', '🏎', '🚓', '🚑', '🚒', '🚐',
      '🛻', '🚚', '🚛', '🚜', '🏍', '🛵', '🚲', '🛴', '🛹', '🛼',
      '🚁', '🛸', '✈', '🛩', '🛫', '🛬', '🪂', '💺', '🚀', '🛰',
      '🚢', '⛵', '🚤', '🛥', '🛳', '⛴', '🚂', '🚃', '🚄', '🚅',
      '🚆', '🚇', '🚈', '🚉', '🚊', '🚝', '🚞', '🚋', '🚌', '🚍',
      '🚘', '🚖', '🚡', '🚠', '🚟', '🎡', '🎢', '🎠', '🏗', '🌁',
    ],
  },
  objects: {
    name: 'Objects',
    icon: '💡',
    emojis: [
      '💡', '🔦', '🕯', '🪔', '🧯', '🛢', '💸', '💵', '💴', '💶',
      '💷', '🪙', '💰', '💳', '💎', '⚖', '🪜', '🧰', '🔧', '🔨',
      '⚒', '🛠', '⛏', '🪓', '🪚', '🔩', '⚙', '🪤', '🧱', '⛓',
      '🧲', '🔫', '💣', '🧨', '🪓', '🔪', '🗡', '⚔', '🛡', '🚬',
      '⚰', '🪦', '⚱', '🏺', '🔮', '📿', '🧿', '💈', '⚗', '🔭',
      '🔬', '🕳', '🩹', '🩺', '💊', '💉', '🩸', '🧬', '🦠', '🧫',
    ],
  },
  symbols: {
    name: 'Symbols',
    icon: '❤️',
    emojis: [
      '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
      '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
      '✝️', '☪️', '🕉', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐',
      '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐',
      '♑', '♒', '♓', '🆔', '⚛️', '🉑', '☢️', '☣️', '📴', '📳',
      '🈶', '🈚', '🈸', '🈺', '🈷️', '✴️', '🆚', '💮', '🉐', '㊙️',
    ],
  },
  flags: {
    name: 'Flags',
    icon: '🏁',
    emojis: [
      '🏁', '🚩', '🎌', '🏴', '🏳️', '🏳️‍🌈', '🏳️‍⚧️', '🏴‍☠️', '🇦🇫', '🇦🇽',
      '🇦🇱', '🇩🇿', '🇦🇸', '🇦🇩', '🇦🇴', '🇦🇮', '🇦🇶', '🇦🇬', '🇦🇷', '🇦🇲',
      '🇦🇼', '🇦🇺', '🇦🇹', '🇦🇿', '🇧🇸', '🇧🇭', '🇧🇩', '🇧🇧', '🇧🇾', '🇧🇪',
      '🇧🇿', '🇧🇯', '🇧🇲', '🇧🇹', '🇧🇴', '🇧🇦', '🇧🇼', '🇧🇷', '🇮🇴', '🇻🇬',
      '🇧🇳', '🇧🇬', '🇧🇫', '🇧🇮', '🇰🇭', '🇨🇲', '🇨🇦', '🇮🇨', '🇨🇻', '🇧🇶',
      '🇰🇾', '🇨🇫', '🇹🇩', '🇨🇱', '🇨🇳', '🇨🇽', '🇨🇨', '🇨🇴', '🇰🇲', '🇨🇬',
    ],
  },
};

const { width: screenWidth } = Dimensions.get('window');

export const EmojiPicker: React.FC<EmojiPickerProps> = ({
  visible,
  onClose,
  onEmojiSelect,
}) => {
  const [selectedCategory, setSelectedCategory] = useState('smileys_people');
  const [searchQuery, setSearchQuery] = useState('');

  const handleEmojiPress = (emoji: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onEmojiSelect(emoji);
  };

  const handleCategoryPress = (categoryKey: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedCategory(categoryKey);
  };

  const filteredEmojis = searchQuery
    ? Object.values(emojiCategories)
        .flatMap(category => category.emojis)
        .filter(emoji => emoji.includes(searchQuery))
    : emojiCategories[selectedCategory as keyof typeof emojiCategories]?.emojis || [];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View className="flex-1 justify-end">
        {/* Backdrop */}
        <TouchableOpacity
          className="flex-1"
          activeOpacity={1}
          onPress={onClose}
        />

        {/* Emoji picker container */}
        <View
          className="rounded-t-3xl"
          style={{
            backgroundColor: chatTheme.colors.surface,
            height: emojiConfig.defaultHeight + 100,
            maxHeight: '70%',
          }}
        >
          {/* Header */}
          <View className="flex-row items-center justify-between p-4 border-b">
            <Text
              className="font-semibold"
              style={{
                color: chatTheme.colors.text,
                fontSize: chatTheme.typography.sizes.lg,
              }}
            >
              Emojis
            </Text>
            <TouchableOpacity onPress={onClose} className="p-2">
              <Ionicons
                name="close"
                size={24}
                color={chatTheme.colors.text}
              />
            </TouchableOpacity>
          </View>

          {/* Search bar */}
          {emojiConfig.showSearchBar && (
            <View
              className="mx-4 mt-2 px-3 py-2 rounded-full flex-row items-center"
              style={{ backgroundColor: chatTheme.colors.background }}
            >
              <Ionicons
                name="search"
                size={16}
                color={chatTheme.colors.textSecondary}
                style={{ marginRight: 8 }}
              />
              <TextInput
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Search emojis..."
                placeholderTextColor={chatTheme.colors.textSecondary}
                className="flex-1"
                style={{
                  color: chatTheme.colors.text,
                  fontSize: chatTheme.typography.sizes.md,
                }}
              />
            </View>
          )}

          {/* Category tabs */}
          {emojiConfig.showTabs && !searchQuery && (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              className="px-2 py-2"
              contentContainerStyle={{ paddingHorizontal: 8 }}
            >
              {Object.entries(emojiCategories).map(([key, category]) => (
                <TouchableOpacity
                  key={key}
                  onPress={() => handleCategoryPress(key)}
                  className={`px-3 py-2 mx-1 rounded-full ${
                    selectedCategory === key ? 'opacity-100' : 'opacity-60'
                  }`}
                  style={{
                    backgroundColor: selectedCategory === key
                      ? chatTheme.colors.primary
                      : chatTheme.colors.background,
                  }}
                >
                  <Text style={{ fontSize: 20 }}>{category.icon}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}

          {/* Emoji grid */}
          <ScrollView
            className="flex-1 px-4"
            showsVerticalScrollIndicator={false}
          >
            <View
              style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                justifyContent: 'space-between',
              }}
            >
              {filteredEmojis.map((emoji, index) => (
                <TouchableOpacity
                  key={`${emoji}-${index}`}
                  onPress={() => handleEmojiPress(emoji)}
                  className="items-center justify-center m-1"
                  style={{
                    width: (screenWidth - 64) / 8, // 8 emojis per row with padding
                    height: (screenWidth - 64) / 8,
                  }}
                >
                  <Text style={{ fontSize: 24 }}>{emoji}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

export default EmojiPicker;
