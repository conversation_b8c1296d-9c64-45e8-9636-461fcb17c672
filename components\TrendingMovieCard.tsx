import React from "react";
import {
  View,
  Text,
  Image,
  FlatList,
  Dimensions,
  StyleSheet,
} from "react-native";

const { width } = Dimensions.get("window");
const cardWidth = width * 0.6; // Adjust card width based on screen size
const cardSpacing = 16; // Spacing between cards

const TrendingMovieCard = ({ item, index }: any) => {
  return (
    <View
      style={[
        styles.cardContainer,
        { width: cardWidth, marginRight: cardSpacing },
      ]}
    >
      <View style={styles.cardContent}>
        <Text style={styles.indexText}>{index + 1}</Text>
        <Image
          source={{
            uri: item.poster_url
              ? `https://image.tmdb.org/t/p/w500/${item.poster_url}`
              : "https://via.placeholder.com/150", // Fallback image
          }}
          style={styles.posterImage}
          resizeMode="cover"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    borderRadius: 16,
    overflow: "hidden",
    backgroundColor: "#1f2937", // Dark background for the card
  },
  cardContent: {
    position: "relative",
  },
  indexText: {
    position: "absolute",
    top: 16,
    left: 16,
    zIndex: 1,
    color: "white",
    fontSize: 24,
    fontWeight: "bold",
    textShadowColor: "rgba(0, 0, 0, 0.75)",
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  posterImage: {
    width: "100%",
    height: 300,
  },
});

export default TrendingMovieCard;
