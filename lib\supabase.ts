import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Replace these with your actual Supabase project URL and anon key
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database types
export interface User {
  id: string;
  email: string;
  username: string;
  avatar_url?: string;
  full_name?: string;
  phone?: string;
  last_seen?: string;
  is_online: boolean;
  created_at: string;
  updated_at: string;
}

export interface Conversation {
  id: string;
  name?: string;
  is_group: boolean;
  avatar_url?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  last_message?: Message;
  unread_count?: number;
}

export interface ConversationParticipant {
  id: string;
  conversation_id: string;
  user_id: string;
  joined_at: string;
  role: 'admin' | 'member';
  is_active: boolean;
}

export interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  message_type: 'text' | 'image' | 'file' | 'audio';
  reply_to?: string;
  created_at: string;
  updated_at: string;
  is_edited: boolean;
  sender?: User;
}

export interface MessageStatus {
  id: string;
  message_id: string;
  user_id: string;
  status: 'sent' | 'delivered' | 'read';
  timestamp: string;
}

export interface TypingIndicator {
  conversation_id: string;
  user_id: string;
  is_typing: boolean;
  timestamp: string;
}

// Auth helper functions
export const signUp = async (email: string, password: string, userData: Partial<User>) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: userData,
    },
  });
  return { data, error };
};

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  return { data, error };
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  return user;
};

// Database helper functions
export const getConversations = async (userId: string) => {
  const { data, error } = await supabase
    .from('conversations')
    .select(`
      *,
      conversation_participants!inner(user_id),
      messages(
        id,
        content,
        created_at,
        sender:users(username, avatar_url)
      )
    `)
    .eq('conversation_participants.user_id', userId)
    .eq('conversation_participants.is_active', true)
    .order('updated_at', { ascending: false });

  return { data, error };
};

export const getMessages = async (conversationId: string, limit = 50, offset = 0) => {
  const { data, error } = await supabase
    .from('messages')
    .select(`
      *,
      sender:users(id, username, avatar_url),
      message_status(status, user_id)
    `)
    .eq('conversation_id', conversationId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  return { data, error };
};

export const sendMessage = async (message: Omit<Message, 'id' | 'created_at' | 'updated_at' | 'is_edited'>) => {
  const { data, error } = await supabase
    .from('messages')
    .insert([message])
    .select(`
      *,
      sender:users(id, username, avatar_url)
    `)
    .single();

  return { data, error };
};

export const markMessageAsRead = async (messageId: string, userId: string) => {
  const { data, error } = await supabase
    .from('message_status')
    .upsert([
      {
        message_id: messageId,
        user_id: userId,
        status: 'read',
        timestamp: new Date().toISOString(),
      },
    ]);

  return { data, error };
};

export const updateTypingStatus = async (conversationId: string, userId: string, isTyping: boolean) => {
  const { data, error } = await supabase
    .from('typing_indicators')
    .upsert([
      {
        conversation_id: conversationId,
        user_id: userId,
        is_typing: isTyping,
        timestamp: new Date().toISOString(),
      },
    ]);

  return { data, error };
};

// Real-time subscriptions
export const subscribeToMessages = (conversationId: string, callback: (payload: any) => void) => {
  return supabase
    .channel(`messages:${conversationId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'messages',
        filter: `conversation_id=eq.${conversationId}`,
      },
      callback
    )
    .subscribe();
};

export const subscribeToTyping = (conversationId: string, callback: (payload: any) => void) => {
  return supabase
    .channel(`typing:${conversationId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'typing_indicators',
        filter: `conversation_id=eq.${conversationId}`,
      },
      callback
    )
    .subscribe();
};

export const subscribeToConversations = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel(`conversations:${userId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'conversations',
      },
      callback
    )
    .subscribe();
};
