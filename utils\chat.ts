import { format, isToday, isYesterday, isThisWeek, isThisYear } from 'date-fns';
import { ChatMessage, ChatConversation, MessageGrouping, ChatUser } from '@/types/chat';

// Date formatting utilities
export const formatMessageTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  return format(date, 'HH:mm');
};

export const formatConversationTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  
  if (isToday(date)) {
    return format(date, 'HH:mm');
  } else if (isYesterday(date)) {
    return 'Yesterday';
  } else if (isThisWeek(date)) {
    return format(date, 'EEEE');
  } else if (isThisYear(date)) {
    return format(date, 'MMM d');
  } else {
    return format(date, 'MMM d, yyyy');
  }
};

export const formatMessageDate = (timestamp: string): string => {
  const date = new Date(timestamp);
  
  if (isToday(date)) {
    return 'Today';
  } else if (isYesterday(date)) {
    return 'Yesterday';
  } else if (isThisYear(date)) {
    return format(date, 'MMMM d');
  } else {
    return format(date, 'MMMM d, yyyy');
  }
};

// Message grouping utilities
export const groupMessagesByDate = (messages: ChatMessage[]): MessageGrouping[] => {
  const groups: { [key: string]: ChatMessage[] } = {};
  
  messages.forEach(message => {
    const dateKey = format(new Date(message.created_at), 'yyyy-MM-dd');
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(message);
  });
  
  return Object.entries(groups)
    .map(([date, messages]) => ({
      date: formatMessageDate(messages[0].created_at),
      messages: messages.sort((a, b) => 
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      ),
    }))
    .sort((a, b) => 
      new Date(a.messages[0].created_at).getTime() - 
      new Date(b.messages[0].created_at).getTime()
    );
};

// Message utilities
export const shouldShowAvatar = (
  message: ChatMessage, 
  nextMessage?: ChatMessage,
  isGroup: boolean = false
): boolean => {
  if (!isGroup) return false;
  if (!nextMessage) return true;
  return message.sender_id !== nextMessage.sender_id;
};

export const shouldShowTimestamp = (
  message: ChatMessage, 
  nextMessage?: ChatMessage
): boolean => {
  if (!nextMessage) return true;
  
  const currentTime = new Date(message.created_at).getTime();
  const nextTime = new Date(nextMessage.created_at).getTime();
  const timeDiff = nextTime - currentTime;
  
  // Show timestamp if messages are more than 5 minutes apart
  return timeDiff > 5 * 60 * 1000;
};

export const shouldShowSenderName = (
  message: ChatMessage, 
  previousMessage?: ChatMessage,
  isGroup: boolean = false
): boolean => {
  if (!isGroup) return false;
  if (!previousMessage) return true;
  return message.sender_id !== previousMessage.sender_id;
};

// Conversation utilities
export const getConversationName = (
  conversation: ChatConversation, 
  currentUserId: string
): string => {
  if (conversation.is_group) {
    return conversation.name || 'Group Chat';
  }
  
  // For 1-on-1 chats, return the other participant's name
  const otherParticipant = conversation.participants?.find(
    p => p.id !== currentUserId
  );
  return otherParticipant?.full_name || otherParticipant?.username || 'Unknown User';
};

export const getConversationAvatar = (
  conversation: ChatConversation, 
  currentUserId: string
): string | undefined => {
  if (conversation.is_group) {
    return conversation.avatar_url;
  }
  
  // For 1-on-1 chats, return the other participant's avatar
  const otherParticipant = conversation.participants?.find(
    p => p.id !== currentUserId
  );
  return otherParticipant?.avatar_url;
};

export const getLastMessagePreview = (message?: ChatMessage): string => {
  if (!message) return 'No messages yet';
  
  switch (message.message_type) {
    case 'image':
      return '📷 Photo';
    case 'file':
      return '📎 File';
    case 'audio':
      return '🎵 Audio';
    default:
      return message.content.length > 50 
        ? `${message.content.substring(0, 50)}...` 
        : message.content;
  }
};

// User status utilities
export const getUserStatus = (user: ChatUser): string => {
  if (user.is_online) {
    return 'online';
  }
  
  if (user.last_seen) {
    const lastSeen = new Date(user.last_seen);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) {
      return 'last seen just now';
    } else if (diffInMinutes < 60) {
      return `last seen ${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    } else if (diffInMinutes < 1440) { // 24 hours
      const hours = Math.floor(diffInMinutes / 60);
      return `last seen ${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (isYesterday(lastSeen)) {
      return `last seen yesterday at ${format(lastSeen, 'HH:mm')}`;
    } else if (isThisWeek(lastSeen)) {
      return `last seen ${format(lastSeen, 'EEEE')} at ${format(lastSeen, 'HH:mm')}`;
    } else {
      return `last seen ${format(lastSeen, 'MMM d')} at ${format(lastSeen, 'HH:mm')}`;
    }
  }
  
  return 'last seen a long time ago';
};

// Message validation
export const isValidMessage = (content: string): boolean => {
  return content.trim().length > 0 && content.length <= 4096;
};

export const sanitizeMessage = (content: string): string => {
  return content.trim().replace(/\s+/g, ' ');
};

// Typing indicator utilities
export const formatTypingUsers = (typingUsers: ChatUser[]): string => {
  if (typingUsers.length === 0) return '';
  
  if (typingUsers.length === 1) {
    return `${typingUsers[0].username} is typing...`;
  } else if (typingUsers.length === 2) {
    return `${typingUsers[0].username} and ${typingUsers[1].username} are typing...`;
  } else {
    return `${typingUsers[0].username} and ${typingUsers.length - 1} others are typing...`;
  }
};

// Search utilities
export const searchMessages = (
  messages: ChatMessage[], 
  query: string
): ChatMessage[] => {
  const lowercaseQuery = query.toLowerCase();
  return messages.filter(message => 
    message.content.toLowerCase().includes(lowercaseQuery) ||
    message.sender?.username?.toLowerCase().includes(lowercaseQuery) ||
    message.sender?.full_name?.toLowerCase().includes(lowercaseQuery)
  );
};

export const searchConversations = (
  conversations: ChatConversation[], 
  query: string,
  currentUserId: string
): ChatConversation[] => {
  const lowercaseQuery = query.toLowerCase();
  return conversations.filter(conversation => {
    const name = getConversationName(conversation, currentUserId).toLowerCase();
    const lastMessage = conversation.last_message?.content?.toLowerCase() || '';
    return name.includes(lowercaseQuery) || lastMessage.includes(lowercaseQuery);
  });
};

// Unread count utilities
export const getTotalUnreadCount = (conversations: ChatConversation[]): number => {
  return conversations.reduce((total, conv) => total + (conv.unread_count || 0), 0);
};

// Message status utilities
export const getMessageStatusIcon = (message: ChatMessage): string => {
  if (!message.isOwn) return '';
  
  if (message.status?.read) return '✓✓'; // Blue double check
  if (message.status?.delivered) return '✓✓'; // Gray double check
  if (message.status?.sent) return '✓'; // Single check
  return '🕐'; // Clock icon for pending
};

// Generate unique conversation ID for 1-on-1 chats
export const generateConversationId = (userId1: string, userId2: string): string => {
  const sortedIds = [userId1, userId2].sort();
  return `${sortedIds[0]}_${sortedIds[1]}`;
};

// Color utilities for avatars
export const getAvatarColor = (userId: string): string => {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ];

  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = userId.charCodeAt(i) + ((hash << 5) - hash);
  }

  return colors[Math.abs(hash) % colors.length];
};

// Message formatting utilities
export const formatMessageText = (text: string): string => {
  // Basic text formatting
  return text
    .replace(/\*\*(.*?)\*\*/g, '<b>$1</b>') // Bold
    .replace(/\*(.*?)\*/g, '<i>$1</i>') // Italic
    .replace(/__(.*?)__/g, '<u>$1</u>') // Underline
    .replace(/~~(.*?)~~/g, '<s>$1</s>') // Strikethrough
    .replace(/`(.*?)`/g, '<code>$1</code>'); // Code
};

export const parseMessageMentions = (text: string, participants: ChatUser[]): string => {
  // Replace @username mentions with formatted mentions
  let formattedText = text;

  participants.forEach(user => {
    const mentionRegex = new RegExp(`@${user.username}\\b`, 'gi');
    formattedText = formattedText.replace(
      mentionRegex,
      `<mention data-user-id="${user.id}">@${user.username}</mention>`
    );
  });

  return formattedText;
};

export const extractUrls = (text: string): string[] => {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return text.match(urlRegex) || [];
};

export const formatMessageWithLinks = (text: string): string => {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return text.replace(urlRegex, '<link href="$1">$1</link>');
};

export const getMessagePreview = (message: ChatMessage): string => {
  switch (message.message_type) {
    case 'text':
      return message.content || '';
    case 'image':
      return '📷 Photo';
    case 'file':
      return '📎 File';
    case 'audio':
      return '🎵 Audio';
    case 'video':
      return '🎥 Video';
    default:
      return 'Message';
  }
};

export const isMessageEdited = (message: ChatMessage): boolean => {
  return message.updated_at !== message.created_at;
};

export const getMessageReactions = (message: ChatMessage): Record<string, string[]> => {
  // Parse reactions from message metadata or separate reactions table
  return message.reactions || {};
};

export const canDeleteMessage = (message: ChatMessage, currentUserId: string): boolean => {
  // Users can delete their own messages within 24 hours
  const messageAge = Date.now() - new Date(message.created_at).getTime();
  const twentyFourHours = 24 * 60 * 60 * 1000;

  return message.sender_id === currentUserId && messageAge < twentyFourHours;
};

export const canEditMessage = (message: ChatMessage, currentUserId: string): boolean => {
  // Users can edit their own text messages within 15 minutes
  const messageAge = Date.now() - new Date(message.created_at).getTime();
  const fifteenMinutes = 15 * 60 * 1000;

  return (
    message.sender_id === currentUserId &&
    message.message_type === 'text' &&
    messageAge < fifteenMinutes
  );
};
