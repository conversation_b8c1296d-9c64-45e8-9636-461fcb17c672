import { ChatTheme } from '@/types/chat';

// WhatsApp-like color scheme
export const chatTheme: ChatTheme = {
  colors: {
    primary: '#25D366', // WhatsApp green
    secondary: '#128C7E', // Darker green
    background: '#0B141A', // Dark background
    surface: '#1F2C34', // Card/surface background
    text: '#E9EDEF', // Primary text color
    textSecondary: '#8696A0', // Secondary text color
    border: '#2A3942', // Border color
    ownMessageBubble: '#005C4B', // Own message bubble (dark green)
    otherMessageBubble: '#1F2C34', // Other message bubble (dark gray)
    ownMessageText: '#E9EDEF', // Text in own messages
    otherMessageText: '#E9EDEF', // Text in other messages
    timestamp: '#8696A0', // Timestamp color
    online: '#25D366', // Online indicator
    offline: '#8696A0', // Offline indicator
    typing: '#25D366', // Typing indicator
    unread: '#25D366', // Unread count background
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 16,
    full: 9999,
  },
  typography: {
    sizes: {
      xs: 12,
      sm: 14,
      md: 16,
      lg: 18,
      xl: 20,
    },
    weights: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
  },
};

// Light theme variant
export const lightChatTheme: ChatTheme = {
  ...chatTheme,
  colors: {
    ...chatTheme.colors,
    background: '#FFFFFF',
    surface: '#F7F8FA',
    text: '#111B21',
    textSecondary: '#667781',
    border: '#E9EDEF',
    ownMessageBubble: '#D9FDD3', // Light green for own messages
    otherMessageBubble: '#FFFFFF', // White for other messages
    ownMessageText: '#111B21',
    otherMessageText: '#111B21',
    timestamp: '#667781',
    offline: '#667781',
  },
};

// Animation configurations
export const animations = {
  message: {
    slideIn: {
      duration: 300,
      type: 'timing' as const,
    },
    fadeIn: {
      duration: 200,
      type: 'timing' as const,
    },
  },
  typing: {
    dotInterval: 500,
    fadeInOut: 1000,
  },
  keyboard: {
    duration: 250,
    type: 'timing' as const,
  },
};

// Layout constants
export const layout = {
  messageMaxWidth: 0.8, // 80% of screen width
  avatarSize: {
    small: 32,
    medium: 40,
    large: 56,
  },
  messageBubble: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginHorizontal: 8,
    marginVertical: 2,
  },
  conversationItem: {
    height: 72,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  header: {
    height: 60,
    paddingHorizontal: 16,
  },
  messageInput: {
    minHeight: 48,
    maxHeight: 120,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
};

// Icon sizes
export const iconSizes = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 28,
};

// Status colors
export const statusColors = {
  sent: '#8696A0',
  delivered: '#8696A0',
  read: '#53BDEB', // WhatsApp blue
  pending: '#8696A0',
  error: '#F15C6D',
};

// Haptic feedback patterns
export const hapticPatterns = {
  light: 'light' as const,
  medium: 'medium' as const,
  heavy: 'heavy' as const,
  selection: 'selection' as const,
  impactLight: 'impactLight' as const,
  impactMedium: 'impactMedium' as const,
  impactHeavy: 'impactHeavy' as const,
  notificationSuccess: 'notificationSuccess' as const,
  notificationWarning: 'notificationWarning' as const,
  notificationError: 'notificationError' as const,
};

// Message bubble styles
export const messageBubbleStyles = {
  own: {
    backgroundColor: chatTheme.colors.ownMessageBubble,
    borderTopLeftRadius: chatTheme.borderRadius.lg,
    borderTopRightRadius: chatTheme.borderRadius.sm,
    borderBottomLeftRadius: chatTheme.borderRadius.lg,
    borderBottomRightRadius: chatTheme.borderRadius.lg,
    marginLeft: 'auto',
    marginRight: chatTheme.spacing.sm,
  },
  other: {
    backgroundColor: chatTheme.colors.otherMessageBubble,
    borderTopLeftRadius: chatTheme.borderRadius.sm,
    borderTopRightRadius: chatTheme.borderRadius.lg,
    borderBottomLeftRadius: chatTheme.borderRadius.lg,
    borderBottomRightRadius: chatTheme.borderRadius.lg,
    marginLeft: chatTheme.spacing.sm,
    marginRight: 'auto',
  },
};

// Emoji picker configuration
export const emojiConfig = {
  categories: [
    'smileys_people',
    'animals_nature',
    'food_drink',
    'activities',
    'travel_places',
    'objects',
    'symbols',
    'flags',
  ],
  showSearchBar: true,
  showSectionTitles: true,
  showTabs: true,
  defaultHeight: 250,
};

// Sound effects (you'll need to add these files to your assets)
export const sounds = {
  messageSent: require('@/assets/sounds/message_sent.mp3'),
  messageReceived: require('@/assets/sounds/message_received.mp3'),
  typing: require('@/assets/sounds/typing.mp3'),
};

// Default avatars for users without profile pictures
export const defaultAvatars = {
  male: require('@/assets/images/default_avatar_male.png'),
  female: require('@/assets/images/default_avatar_female.png'),
  group: require('@/assets/images/default_group_avatar.png'),
};

// Message limits
export const messageLimits = {
  maxLength: 4096,
  maxFileSize: 100 * 1024 * 1024, // 100MB
  maxImageSize: 16 * 1024 * 1024, // 16MB
  maxAudioDuration: 600, // 10 minutes in seconds
};

// Typing indicator configuration
export const typingConfig = {
  debounceDelay: 1000, // Stop typing after 1 second of inactivity
  showDelay: 500, // Show typing indicator after 500ms
  hideDelay: 3000, // Hide typing indicator after 3 seconds
};

// Pagination configuration
export const pagination = {
  messagesPerPage: 50,
  conversationsPerPage: 20,
  loadMoreThreshold: 10, // Load more when 10 items from the end
};

// Cache configuration
export const cache = {
  maxMessages: 1000, // Maximum messages to keep in memory per conversation
  maxConversations: 100, // Maximum conversations to keep in memory
  imageCache: {
    maxSize: 50 * 1024 * 1024, // 50MB
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
};
