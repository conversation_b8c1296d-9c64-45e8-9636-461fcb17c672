import React from 'react';
import { View, Text, TouchableOpacity, Image, Pressable } from 'react-native';
import { ChatMessage } from '@/types/chat';
import { formatMessageTime, getMessageStatusIcon } from '@/utils/chat';
import { chatTheme, layout, messageBubbleStyles } from '@/constants/theme';
import * as Haptics from 'expo-haptics';

interface MessageBubbleProps {
  message: ChatMessage;
  isOwn: boolean;
  showAvatar: boolean;
  showTimestamp: boolean;
  showSenderName: boolean;
  onLongPress?: () => void;
  onReply?: () => void;
  onPress?: () => void;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwn,
  showAvatar,
  showTimestamp,
  showSenderName,
  onLongPress,
  onReply,
  onPress,
}) => {
  const handleLongPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onLongPress?.();
  };

  const handlePress = () => {
    onPress?.();
  };

  const bubbleStyle = isOwn ? messageBubbleStyles.own : messageBubbleStyles.other;
  const textColor = isOwn ? chatTheme.colors.ownMessageText : chatTheme.colors.otherMessageText;

  return (
    <View className="flex-row items-end mb-1" style={{ marginVertical: layout.messageBubble.marginVertical }}>
      {/* Avatar for other users */}
      {!isOwn && showAvatar && (
        <View className="mr-2 mb-1">
          {message.sender?.avatar_url ? (
            <Image
              source={{ uri: message.sender.avatar_url }}
              className="rounded-full"
              style={{
                width: layout.avatarSize.small,
                height: layout.avatarSize.small,
              }}
            />
          ) : (
            <View
              className="rounded-full items-center justify-center"
              style={{
                width: layout.avatarSize.small,
                height: layout.avatarSize.small,
                backgroundColor: chatTheme.colors.secondary,
              }}
            >
              <Text
                className="font-medium text-white"
                style={{ fontSize: chatTheme.typography.sizes.sm }}
              >
                {message.sender?.username?.charAt(0).toUpperCase() || '?'}
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Spacer for own messages when avatar would be shown */}
      {!isOwn && !showAvatar && (
        <View style={{ width: layout.avatarSize.small + 8 }} />
      )}

      {/* Message bubble */}
      <Pressable
        onPress={handlePress}
        onLongPress={handleLongPress}
        className="max-w-[80%] px-3 py-2"
        style={[
          bubbleStyle,
          {
            paddingHorizontal: layout.messageBubble.paddingHorizontal,
            paddingVertical: layout.messageBubble.paddingVertical,
          },
        ]}
      >
        {/* Sender name for group chats */}
        {!isOwn && showSenderName && (
          <Text
            className="font-medium mb-1"
            style={{
              color: chatTheme.colors.primary,
              fontSize: chatTheme.typography.sizes.sm,
            }}
          >
            {message.sender?.username || 'Unknown'}
          </Text>
        )}

        {/* Reply indicator */}
        {message.reply_to && (
          <View
            className="border-l-2 pl-2 mb-2 opacity-70"
            style={{ borderLeftColor: isOwn ? chatTheme.colors.ownMessageText : chatTheme.colors.primary }}
          >
            <Text
              className="text-xs"
              style={{ color: textColor }}
              numberOfLines={1}
            >
              Replying to message...
            </Text>
          </View>
        )}

        {/* Message content */}
        <View>
          {message.message_type === 'text' ? (
            <Text
              className="leading-5"
              style={{
                color: textColor,
                fontSize: chatTheme.typography.sizes.md,
              }}
              selectable
            >
              {message.content}
            </Text>
          ) : message.message_type === 'image' ? (
            <View>
              <Image
                source={{ uri: message.content }}
                className="rounded-lg mb-1"
                style={{
                  width: 200,
                  height: 200,
                  resizeMode: 'cover',
                }}
              />
            </View>
          ) : (
            <View className="flex-row items-center">
              <Text
                style={{
                  color: textColor,
                  fontSize: chatTheme.typography.sizes.md,
                }}
              >
                📎 {message.message_type === 'audio' ? 'Audio' : 'File'}
              </Text>
            </View>
          )}

          {/* Edited indicator */}
          {message.is_edited && (
            <Text
              className="text-xs mt-1 opacity-70"
              style={{ color: textColor }}
            >
              edited
            </Text>
          )}
        </View>

        {/* Timestamp and status */}
        <View className="flex-row items-center justify-end mt-1">
          <Text
            className="text-xs mr-1"
            style={{
              color: chatTheme.colors.timestamp,
              fontSize: chatTheme.typography.sizes.xs,
            }}
          >
            {formatMessageTime(message.created_at)}
          </Text>

          {/* Message status for own messages */}
          {isOwn && (
            <Text
              className="text-xs"
              style={{
                color: message.status?.read ? '#53BDEB' : chatTheme.colors.timestamp,
                fontSize: chatTheme.typography.sizes.xs,
              }}
            >
              {getMessageStatusIcon(message)}
            </Text>
          )}
        </View>
      </Pressable>
    </View>
  );
};

// Message bubble with animation wrapper
export const AnimatedMessageBubble: React.FC<MessageBubbleProps> = (props) => {
  return (
    <View className="animate-fade-in">
      <MessageBubble {...props} />
    </View>
  );
};

export default MessageBubble;
