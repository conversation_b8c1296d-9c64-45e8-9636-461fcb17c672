import { 
  formatMessageTime, 
  formatConversationTime, 
  groupMessagesByDate,
  shouldShowAvatar,
  shouldShowTimestamp,
  isValidMessage,
  sanitizeMessage,
  formatMessageText,
  canDeleteMessage,
  canEditMessage,
} from '@/utils/chat';
import { ChatMessage, ChatUser } from '@/types/chat';

// Mock data
const mockUser: ChatUser = {
  id: 'user1',
  username: 'testuser',
  email: '<EMAIL>',
  full_name: 'Test User',
  avatar_url: null,
  is_online: true,
  last_seen: new Date().toISOString(),
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

const mockMessage: ChatMessage = {
  id: 'msg1',
  conversation_id: 'conv1',
  sender_id: 'user1',
  content: 'Hello, world!',
  message_type: 'text',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  isOwn: true,
};

describe('Chat Utilities', () => {
  describe('formatMessageTime', () => {
    it('should format time correctly', () => {
      const now = new Date();
      const timeString = formatMessageTime(now.toISOString());
      expect(timeString).toMatch(/^\d{2}:\d{2}$/);
    });
  });

  describe('formatConversationTime', () => {
    it('should format recent time as time only', () => {
      const now = new Date();
      const timeString = formatConversationTime(now.toISOString());
      expect(timeString).toMatch(/^\d{2}:\d{2}$/);
    });

    it('should format yesterday as "Yesterday"', () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const timeString = formatConversationTime(yesterday.toISOString());
      expect(timeString).toBe('Yesterday');
    });
  });

  describe('groupMessagesByDate', () => {
    it('should group messages by date', () => {
      const today = new Date();
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      const messages: ChatMessage[] = [
        { ...mockMessage, id: 'msg1', created_at: today.toISOString() },
        { ...mockMessage, id: 'msg2', created_at: yesterday.toISOString() },
        { ...mockMessage, id: 'msg3', created_at: today.toISOString() },
      ];

      const grouped = groupMessagesByDate(messages);
      expect(Object.keys(grouped)).toHaveLength(2);
      expect(grouped['Today']).toHaveLength(2);
      expect(grouped['Yesterday']).toHaveLength(1);
    });
  });

  describe('shouldShowAvatar', () => {
    it('should show avatar for last message in group', () => {
      const message1 = { ...mockMessage, sender_id: 'user1' };
      const message2 = { ...mockMessage, sender_id: 'user2' };
      
      expect(shouldShowAvatar(message1, message2, true)).toBe(true);
    });

    it('should not show avatar for consecutive messages from same sender', () => {
      const message1 = { ...mockMessage, sender_id: 'user1' };
      const message2 = { ...mockMessage, sender_id: 'user1' };
      
      expect(shouldShowAvatar(message1, message2, true)).toBe(false);
    });
  });

  describe('shouldShowTimestamp', () => {
    it('should show timestamp for messages far apart', () => {
      const now = new Date();
      const earlier = new Date(now.getTime() - 10 * 60 * 1000); // 10 minutes ago

      const message1 = { ...mockMessage, created_at: earlier.toISOString() };
      const message2 = { ...mockMessage, created_at: now.toISOString() };
      
      expect(shouldShowTimestamp(message1, message2)).toBe(true);
    });

    it('should not show timestamp for recent messages', () => {
      const now = new Date();
      const recent = new Date(now.getTime() - 2 * 60 * 1000); // 2 minutes ago

      const message1 = { ...mockMessage, created_at: recent.toISOString() };
      const message2 = { ...mockMessage, created_at: now.toISOString() };
      
      expect(shouldShowTimestamp(message1, message2)).toBe(false);
    });
  });

  describe('isValidMessage', () => {
    it('should validate non-empty messages', () => {
      expect(isValidMessage('Hello')).toBe(true);
      expect(isValidMessage('   Hello   ')).toBe(true);
    });

    it('should reject empty messages', () => {
      expect(isValidMessage('')).toBe(false);
      expect(isValidMessage('   ')).toBe(false);
    });

    it('should reject messages that are too long', () => {
      const longMessage = 'a'.repeat(5000);
      expect(isValidMessage(longMessage)).toBe(false);
    });
  });

  describe('sanitizeMessage', () => {
    it('should trim and normalize whitespace', () => {
      expect(sanitizeMessage('  Hello   world  ')).toBe('Hello world');
      expect(sanitizeMessage('Hello\n\nworld')).toBe('Hello world');
    });
  });

  describe('formatMessageText', () => {
    it('should format bold text', () => {
      expect(formatMessageText('**bold**')).toBe('<b>bold</b>');
    });

    it('should format italic text', () => {
      expect(formatMessageText('*italic*')).toBe('<i>italic</i>');
    });

    it('should format code text', () => {
      expect(formatMessageText('`code`')).toBe('<code>code</code>');
    });
  });

  describe('canDeleteMessage', () => {
    it('should allow deletion of own recent messages', () => {
      const recentMessage = {
        ...mockMessage,
        sender_id: 'user1',
        created_at: new Date().toISOString(),
      };
      
      expect(canDeleteMessage(recentMessage, 'user1')).toBe(true);
    });

    it('should not allow deletion of old messages', () => {
      const oldMessage = {
        ...mockMessage,
        sender_id: 'user1',
        created_at: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString(), // 25 hours ago
      };
      
      expect(canDeleteMessage(oldMessage, 'user1')).toBe(false);
    });

    it('should not allow deletion of other users messages', () => {
      const otherMessage = {
        ...mockMessage,
        sender_id: 'user2',
        created_at: new Date().toISOString(),
      };
      
      expect(canDeleteMessage(otherMessage, 'user1')).toBe(false);
    });
  });

  describe('canEditMessage', () => {
    it('should allow editing of own recent text messages', () => {
      const recentMessage = {
        ...mockMessage,
        sender_id: 'user1',
        message_type: 'text' as const,
        created_at: new Date().toISOString(),
      };
      
      expect(canEditMessage(recentMessage, 'user1')).toBe(true);
    });

    it('should not allow editing of old messages', () => {
      const oldMessage = {
        ...mockMessage,
        sender_id: 'user1',
        message_type: 'text' as const,
        created_at: new Date(Date.now() - 20 * 60 * 1000).toISOString(), // 20 minutes ago
      };
      
      expect(canEditMessage(oldMessage, 'user1')).toBe(false);
    });

    it('should not allow editing of non-text messages', () => {
      const imageMessage = {
        ...mockMessage,
        sender_id: 'user1',
        message_type: 'image' as const,
        created_at: new Date().toISOString(),
      };
      
      expect(canEditMessage(imageMessage, 'user1')).toBe(false);
    });
  });
});

// Error handling tests
describe('Error Handling', () => {
  // These would be more comprehensive in a real test suite
  it('should handle network errors gracefully', () => {
    // Mock network error scenarios
    expect(true).toBe(true); // Placeholder
  });

  it('should handle authentication errors', () => {
    // Mock auth error scenarios
    expect(true).toBe(true); // Placeholder
  });
});

// Offline queue tests
describe('Offline Message Queue', () => {
  it('should queue messages when offline', () => {
    // Mock offline scenarios
    expect(true).toBe(true); // Placeholder
  });

  it('should process queue when coming back online', () => {
    // Mock reconnection scenarios
    expect(true).toBe(true); // Placeholder
  });
});
