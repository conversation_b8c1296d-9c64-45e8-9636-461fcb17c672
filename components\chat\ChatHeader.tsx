import React from 'react';
import { View, Text, TouchableOpacity, Image, StatusBar } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ChatConversation, TypingUser } from '@/types/chat';
import { chatTheme, layout } from '@/constants/theme';
import { getConversationName, getConversationAvatar, getUserStatus, formatTypingUsers } from '@/utils/chat';
import * as Haptics from 'expo-haptics';

interface ChatHeaderProps {
  conversation: ChatConversation;
  currentUserId: string;
  onBack: () => void;
  onProfilePress?: () => void;
  onCallPress?: () => void;
  onVideoCallPress?: () => void;
  onMenuPress?: () => void;
  typingUsers?: TypingUser[];
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({
  conversation,
  currentUserId,
  onBack,
  onProfilePress,
  onCallPress,
  onVideoCallPress,
  onMenuPress,
  typingUsers = [],
}) => {
  const conversationName = getConversationName(conversation, currentUserId);
  const conversationAvatar = getConversationAvatar(conversation, currentUserId);
  const otherParticipant = conversation.participants?.find(p => p.id !== currentUserId);
  
  const handleBack = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onBack();
  };

  const handleProfilePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onProfilePress?.();
  };

  const handleCallPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onCallPress?.();
  };

  const handleVideoCallPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onVideoCallPress?.();
  };

  const handleMenuPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onMenuPress?.();
  };

  const getStatusText = () => {
    if (typingUsers.length > 0) {
      return formatTypingUsers(typingUsers);
    }
    
    if (conversation.is_group) {
      const participantCount = conversation.participants?.length || 0;
      return `${participantCount} participants`;
    }
    
    if (otherParticipant) {
      return getUserStatus(otherParticipant);
    }
    
    return 'Unknown';
  };

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor={chatTheme.colors.surface} />
      <View
        className="flex-row items-center justify-between px-4 pt-2 pb-2"
        style={{
          backgroundColor: chatTheme.colors.surface,
          height: layout.header.height,
          paddingTop: StatusBar.currentHeight || 0,
        }}
      >
        {/* Left section - Back button and conversation info */}
        <View className="flex-row items-center flex-1">
          {/* Back button */}
          <TouchableOpacity
            onPress={handleBack}
            className="mr-3 p-2"
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={chatTheme.colors.text}
            />
          </TouchableOpacity>

          {/* Avatar */}
          <TouchableOpacity
            onPress={handleProfilePress}
            className="mr-3"
            disabled={!onProfilePress}
          >
            {conversationAvatar ? (
              <Image
                source={{ uri: conversationAvatar }}
                className="rounded-full"
                style={{
                  width: layout.avatarSize.medium,
                  height: layout.avatarSize.medium,
                }}
              />
            ) : (
              <View
                className="rounded-full items-center justify-center"
                style={{
                  width: layout.avatarSize.medium,
                  height: layout.avatarSize.medium,
                  backgroundColor: chatTheme.colors.primary,
                }}
              >
                <Text
                  className="font-bold text-white"
                  style={{ fontSize: chatTheme.typography.sizes.lg }}
                >
                  {conversationName.charAt(0).toUpperCase()}
                </Text>
              </View>
            )}

            {/* Online indicator for 1-on-1 chats */}
            {!conversation.is_group && otherParticipant?.is_online && (
              <View
                className="absolute bottom-0 right-0 rounded-full border-2"
                style={{
                  width: 12,
                  height: 12,
                  backgroundColor: chatTheme.colors.online,
                  borderColor: chatTheme.colors.surface,
                }}
              />
            )}
          </TouchableOpacity>

          {/* Conversation info */}
          <TouchableOpacity
            onPress={handleProfilePress}
            className="flex-1"
            disabled={!onProfilePress}
          >
            <Text
              className="font-semibold"
              style={{
                color: chatTheme.colors.text,
                fontSize: chatTheme.typography.sizes.lg,
              }}
              numberOfLines={1}
            >
              {conversationName}
            </Text>
            
            <Text
              className={`${typingUsers.length > 0 ? 'font-medium' : ''}`}
              style={{
                color: typingUsers.length > 0 
                  ? chatTheme.colors.typing 
                  : chatTheme.colors.textSecondary,
                fontSize: chatTheme.typography.sizes.sm,
              }}
              numberOfLines={1}
            >
              {getStatusText()}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Right section - Action buttons */}
        <View className="flex-row items-center">
          {/* Video call button */}
          {onVideoCallPress && !conversation.is_group && (
            <TouchableOpacity
              onPress={handleVideoCallPress}
              className="p-2 mr-1"
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons
                name="videocam"
                size={24}
                color={chatTheme.colors.text}
              />
            </TouchableOpacity>
          )}

          {/* Voice call button */}
          {onCallPress && !conversation.is_group && (
            <TouchableOpacity
              onPress={handleCallPress}
              className="p-2 mr-1"
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons
                name="call"
                size={24}
                color={chatTheme.colors.text}
              />
            </TouchableOpacity>
          )}

          {/* Menu button */}
          {onMenuPress && (
            <TouchableOpacity
              onPress={handleMenuPress}
              className="p-2"
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons
                name="ellipsis-vertical"
                size={24}
                color={chatTheme.colors.text}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </>
  );
};

export default ChatHeader;
