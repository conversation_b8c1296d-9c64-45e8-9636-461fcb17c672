import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  Text,
  Animated,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ChatMessage } from '@/types/chat';
import { chatTheme, layout, messageLimits } from '@/constants/theme';
import { isValidMessage, sanitizeMessage } from '@/utils/chat';
import * as Haptics from 'expo-haptics';

interface MessageInputProps {
  onSendMessage: (text: string, replyTo?: string) => void;
  onTypingChange: (isTyping: boolean) => void;
  replyTo?: ChatMessage;
  onCancelReply?: () => void;
  placeholder?: string;
  disabled?: boolean;
}

export const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  onTypingChange,
  replyTo,
  onCancelReply,
  placeholder = "Type a message...",
  disabled = false,
}) => {
  const [text, setText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [inputHeight, setInputHeight] = useState(layout.messageInput.minHeight);
  
  const textInputRef = useRef<TextInput>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const sendButtonScale = useRef(new Animated.Value(0)).current;

  // Handle typing indicator
  useEffect(() => {
    const shouldShowTyping = text.trim().length > 0;
    
    if (shouldShowTyping !== isTyping) {
      setIsTyping(shouldShowTyping);
      onTypingChange(shouldShowTyping);
    }

    // Clear typing indicator after delay
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    if (shouldShowTyping) {
      typingTimeoutRef.current = setTimeout(() => {
        setIsTyping(false);
        onTypingChange(false);
      }, 3000);
    }

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [text, isTyping, onTypingChange]);

  // Animate send button
  useEffect(() => {
    Animated.spring(sendButtonScale, {
      toValue: text.trim().length > 0 ? 1 : 0,
      useNativeDriver: true,
      tension: 150,
      friction: 8,
    }).start();
  }, [text, sendButtonScale]);

  const handleSend = () => {
    const sanitizedText = sanitizeMessage(text);
    
    if (!isValidMessage(sanitizedText) || disabled) {
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onSendMessage(sanitizedText, replyTo?.id);
    setText('');
    setInputHeight(layout.messageInput.minHeight);
    
    // Stop typing indicator
    setIsTyping(false);
    onTypingChange(false);
  };

  const handleTextChange = (newText: string) => {
    if (newText.length <= messageLimits.maxLength) {
      setText(newText);
    }
  };

  const handleContentSizeChange = (event: any) => {
    const { height } = event.nativeEvent.contentSize;
    const newHeight = Math.min(
      Math.max(height + 16, layout.messageInput.minHeight),
      layout.messageInput.maxHeight
    );
    setInputHeight(newHeight);
  };

  const handleCancelReply = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onCancelReply?.();
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <View
        className="border-t"
        style={{
          backgroundColor: chatTheme.colors.surface,
          borderTopColor: chatTheme.colors.border,
          paddingHorizontal: layout.messageInput.paddingHorizontal,
          paddingVertical: layout.messageInput.paddingVertical,
        }}
      >
        {/* Reply indicator */}
        {replyTo && (
          <View
            className="flex-row items-center justify-between p-3 mb-2 rounded-lg"
            style={{ backgroundColor: chatTheme.colors.background }}
          >
            <View className="flex-1">
              <Text
                className="font-medium mb-1"
                style={{
                  color: chatTheme.colors.primary,
                  fontSize: chatTheme.typography.sizes.sm,
                }}
              >
                Replying to {replyTo.sender?.username || 'Unknown'}
              </Text>
              <Text
                className="opacity-70"
                style={{
                  color: chatTheme.colors.text,
                  fontSize: chatTheme.typography.sizes.sm,
                }}
                numberOfLines={1}
              >
                {replyTo.content}
              </Text>
            </View>
            <TouchableOpacity
              onPress={handleCancelReply}
              className="ml-3 p-1"
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons
                name="close"
                size={20}
                color={chatTheme.colors.textSecondary}
              />
            </TouchableOpacity>
          </View>
        )}

        {/* Input container */}
        <View className="flex-row items-end">
          {/* Text input */}
          <View
            className="flex-1 rounded-full border mr-3"
            style={{
              backgroundColor: chatTheme.colors.background,
              borderColor: chatTheme.colors.border,
              minHeight: layout.messageInput.minHeight,
              height: inputHeight,
            }}
          >
            <TextInput
              ref={textInputRef}
              value={text}
              onChangeText={handleTextChange}
              onContentSizeChange={handleContentSizeChange}
              placeholder={placeholder}
              placeholderTextColor={chatTheme.colors.textSecondary}
              multiline
              textAlignVertical="center"
              editable={!disabled}
              className="px-4 py-3 flex-1"
              style={{
                color: chatTheme.colors.text,
                fontSize: chatTheme.typography.sizes.md,
                maxHeight: layout.messageInput.maxHeight - 16,
              }}
              returnKeyType="default"
              blurOnSubmit={false}
            />
          </View>

          {/* Send button */}
          <Animated.View
            style={{
              transform: [{ scale: sendButtonScale }],
              opacity: sendButtonScale,
            }}
          >
            <TouchableOpacity
              onPress={handleSend}
              disabled={!text.trim() || disabled}
              className="rounded-full items-center justify-center"
              style={{
                backgroundColor: chatTheme.colors.primary,
                width: layout.messageInput.minHeight,
                height: layout.messageInput.minHeight,
              }}
            >
              <Ionicons
                name="send"
                size={20}
                color="white"
              />
            </TouchableOpacity>
          </Animated.View>

          {/* Attachment button (when no text) */}
          {!text.trim() && (
            <TouchableOpacity
              className="rounded-full items-center justify-center ml-2"
              style={{
                backgroundColor: chatTheme.colors.textSecondary,
                width: layout.messageInput.minHeight,
                height: layout.messageInput.minHeight,
              }}
              onPress={() => {
                // TODO: Implement attachment functionality
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
            >
              <Ionicons
                name="add"
                size={20}
                color="white"
              />
            </TouchableOpacity>
          )}
        </View>

        {/* Character count (when approaching limit) */}
        {text.length > messageLimits.maxLength * 0.9 && (
          <Text
            className="text-right mt-1"
            style={{
              color: text.length >= messageLimits.maxLength 
                ? '#F15C6D' 
                : chatTheme.colors.textSecondary,
              fontSize: chatTheme.typography.sizes.xs,
            }}
          >
            {text.length}/{messageLimits.maxLength}
          </Text>
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

export default MessageInput;
