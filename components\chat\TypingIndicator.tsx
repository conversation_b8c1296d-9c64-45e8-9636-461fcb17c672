import React, { useEffect, useRef } from 'react';
import { View, Text, Animated } from 'react-native';
import { TypingUser } from '@/types/chat';
import { chatTheme, layout, messageBubbleStyles } from '@/constants/theme';
import { formatTypingUsers } from '@/utils/chat';

interface TypingIndicatorProps {
  typingUsers: TypingUser[];
  visible: boolean;
}

const TypingDots: React.FC = () => {
  const dot1 = useRef(new Animated.Value(0)).current;
  const dot2 = useRef(new Animated.Value(0)).current;
  const dot3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animateDots = () => {
      const createDotAnimation = (dot: Animated.Value, delay: number) => {
        return Animated.sequence([
          Animated.delay(delay),
          Animated.timing(dot, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(dot, {
            toValue: 0,
            duration: 400,
            useNativeDriver: true,
          }),
        ]);
      };

      const animation = Animated.loop(
        Animated.parallel([
          createDotAnimation(dot1, 0),
          createDotAnimation(dot2, 200),
          createDotAnimation(dot3, 400),
        ])
      );

      animation.start();

      return () => animation.stop();
    };

    const cleanup = animateDots();
    return cleanup;
  }, [dot1, dot2, dot3]);

  const dotStyle = {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: chatTheme.colors.textSecondary,
    marginHorizontal: 1,
  };

  return (
    <View className="flex-row items-center justify-center py-1">
      <Animated.View
        style={[
          dotStyle,
          {
            opacity: dot1,
            transform: [
              {
                translateY: dot1.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -3],
                }),
              },
            ],
          },
        ]}
      />
      <Animated.View
        style={[
          dotStyle,
          {
            opacity: dot2,
            transform: [
              {
                translateY: dot2.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -3],
                }),
              },
            ],
          },
        ]}
      />
      <Animated.View
        style={[
          dotStyle,
          {
            opacity: dot3,
            transform: [
              {
                translateY: dot3.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -3],
                }),
              },
            ],
          },
        ]}
      />
    </View>
  );
};

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  typingUsers,
  visible,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;

  useEffect(() => {
    if (visible && typingUsers.length > 0) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 20,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, typingUsers.length, fadeAnim, slideAnim]);

  if (!visible || typingUsers.length === 0) {
    return null;
  }

  return (
    <Animated.View
      className="flex-row items-end mb-2"
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
        marginVertical: layout.messageBubble.marginVertical,
      }}
    >
      {/* Avatar for the first typing user */}
      <View className="mr-2 mb-1">
        {typingUsers[0]?.avatar_url ? (
          <Animated.Image
            source={{ uri: typingUsers[0].avatar_url }}
            className="rounded-full"
            style={{
              width: layout.avatarSize.small,
              height: layout.avatarSize.small,
            }}
          />
        ) : (
          <View
            className="rounded-full items-center justify-center"
            style={{
              width: layout.avatarSize.small,
              height: layout.avatarSize.small,
              backgroundColor: chatTheme.colors.secondary,
            }}
          >
            <Text
              className="font-medium text-white"
              style={{ fontSize: chatTheme.typography.sizes.sm }}
            >
              {typingUsers[0]?.username?.charAt(0).toUpperCase() || '?'}
            </Text>
          </View>
        )}
      </View>

      {/* Typing bubble */}
      <View
        className="px-4 py-3 rounded-lg"
        style={[
          messageBubbleStyles.other,
          {
            backgroundColor: chatTheme.colors.otherMessageBubble,
            minWidth: 60,
          },
        ]}
      >
        <TypingDots />
      </View>
    </Animated.View>
  );
};

// Simple typing indicator for chat header
export const SimpleTypingIndicator: React.FC<{ typingUsers: TypingUser[] }> = ({
  typingUsers,
}) => {
  if (typingUsers.length === 0) return null;

  return (
    <View className="flex-row items-center">
      <Text
        style={{
          color: chatTheme.colors.typing,
          fontSize: chatTheme.typography.sizes.sm,
        }}
      >
        {formatTypingUsers(typingUsers)}
      </Text>
      <View className="ml-2">
        <TypingDots />
      </View>
    </View>
  );
};

export default TypingIndicator;
