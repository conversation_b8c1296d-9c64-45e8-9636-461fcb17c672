import { Alert } from 'react-native';

export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

export class ChatError extends Error {
  code: string;
  details?: any;
  timestamp: Date;

  constructor(code: string, message: string, details?: any) {
    super(message);
    this.name = 'ChatError';
    this.code = code;
    this.details = details;
    this.timestamp = new Date();
  }
}

// Error codes
export const ERROR_CODES = {
  // Network errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  CONNECTION_TIMEOUT: 'CONNECTION_TIMEOUT',
  SERVER_ERROR: 'SERVER_ERROR',
  
  // Authentication errors
  AUTH_REQUIRED: 'AUTH_REQUIRED',
  AUTH_EXPIRED: 'AUTH_EXPIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  
  // Message errors
  MESSAGE_SEND_FAILED: 'MESSAGE_SEND_FAILED',
  MESSAGE_TOO_LONG: 'MESSAGE_TOO_LONG',
  INVALID_MESSAGE_TYPE: 'INVALID_MESSAGE_TYPE',
  
  // Conversation errors
  CONVERSATION_NOT_FOUND: 'CONVERSATION_NOT_FOUND',
  CONVERSATION_ACCESS_DENIED: 'CONVERSATION_ACCESS_DENIED',
  
  // File upload errors
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  
  // General errors
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
} as const;

// Error messages
export const ERROR_MESSAGES = {
  [ERROR_CODES.NETWORK_ERROR]: 'Network connection failed. Please check your internet connection.',
  [ERROR_CODES.CONNECTION_TIMEOUT]: 'Connection timed out. Please try again.',
  [ERROR_CODES.SERVER_ERROR]: 'Server error occurred. Please try again later.',
  
  [ERROR_CODES.AUTH_REQUIRED]: 'Authentication required. Please sign in.',
  [ERROR_CODES.AUTH_EXPIRED]: 'Session expired. Please sign in again.',
  [ERROR_CODES.INVALID_CREDENTIALS]: 'Invalid credentials. Please check your login details.',
  
  [ERROR_CODES.MESSAGE_SEND_FAILED]: 'Failed to send message. Please try again.',
  [ERROR_CODES.MESSAGE_TOO_LONG]: 'Message is too long. Please shorten your message.',
  [ERROR_CODES.INVALID_MESSAGE_TYPE]: 'Invalid message type.',
  
  [ERROR_CODES.CONVERSATION_NOT_FOUND]: 'Conversation not found.',
  [ERROR_CODES.CONVERSATION_ACCESS_DENIED]: 'Access denied to this conversation.',
  
  [ERROR_CODES.FILE_TOO_LARGE]: 'File is too large. Maximum size is 10MB.',
  [ERROR_CODES.INVALID_FILE_TYPE]: 'Invalid file type. Please select a supported file.',
  [ERROR_CODES.UPLOAD_FAILED]: 'File upload failed. Please try again.',
  
  [ERROR_CODES.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again.',
  [ERROR_CODES.VALIDATION_ERROR]: 'Validation error. Please check your input.',
} as const;

// Error handler function
export const handleError = (error: any, showAlert = true): AppError => {
  console.error('Error occurred:', error);

  let appError: AppError;

  if (error instanceof ChatError) {
    appError = {
      code: error.code,
      message: error.message,
      details: error.details,
      timestamp: error.timestamp,
    };
  } else if (error?.code && ERROR_MESSAGES[error.code as keyof typeof ERROR_MESSAGES]) {
    appError = {
      code: error.code,
      message: ERROR_MESSAGES[error.code as keyof typeof ERROR_MESSAGES],
      details: error,
      timestamp: new Date(),
    };
  } else {
    // Handle common error patterns
    let code = ERROR_CODES.UNKNOWN_ERROR;
    let message = ERROR_MESSAGES[ERROR_CODES.UNKNOWN_ERROR];

    if (error?.message?.includes('network') || error?.message?.includes('fetch')) {
      code = ERROR_CODES.NETWORK_ERROR;
      message = ERROR_MESSAGES[ERROR_CODES.NETWORK_ERROR];
    } else if (error?.message?.includes('timeout')) {
      code = ERROR_CODES.CONNECTION_TIMEOUT;
      message = ERROR_MESSAGES[ERROR_CODES.CONNECTION_TIMEOUT];
    } else if (error?.status >= 500) {
      code = ERROR_CODES.SERVER_ERROR;
      message = ERROR_MESSAGES[ERROR_CODES.SERVER_ERROR];
    } else if (error?.status === 401) {
      code = ERROR_CODES.AUTH_EXPIRED;
      message = ERROR_MESSAGES[ERROR_CODES.AUTH_EXPIRED];
    } else if (error?.status === 403) {
      code = ERROR_CODES.CONVERSATION_ACCESS_DENIED;
      message = ERROR_MESSAGES[ERROR_CODES.CONVERSATION_ACCESS_DENIED];
    }

    appError = {
      code,
      message,
      details: error,
      timestamp: new Date(),
    };
  }

  if (showAlert) {
    showErrorAlert(appError);
  }

  return appError;
};

// Show error alert
export const showErrorAlert = (error: AppError) => {
  Alert.alert(
    'Error',
    error.message,
    [
      {
        text: 'OK',
        style: 'default',
      },
    ]
  );
};

// Retry mechanism
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }

      // Exponential backoff
      const waitTime = delay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  throw lastError;
};

// Network status checker
export const isNetworkError = (error: any): boolean => {
  return (
    error?.code === ERROR_CODES.NETWORK_ERROR ||
    error?.code === ERROR_CODES.CONNECTION_TIMEOUT ||
    error?.message?.includes('network') ||
    error?.message?.includes('fetch') ||
    error?.message?.includes('timeout')
  );
};

// Validation helpers
export const validateMessage = (content: string): void => {
  if (!content || content.trim().length === 0) {
    throw new ChatError(ERROR_CODES.VALIDATION_ERROR, 'Message cannot be empty');
  }

  if (content.length > 4096) {
    throw new ChatError(ERROR_CODES.MESSAGE_TOO_LONG, ERROR_MESSAGES[ERROR_CODES.MESSAGE_TOO_LONG]);
  }
};

export const validateFile = (file: { size: number; type: string }): void => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'video/mp4',
    'video/quicktime',
    'audio/mpeg',
    'audio/wav',
    'application/pdf',
    'text/plain',
  ];

  if (file.size > maxSize) {
    throw new ChatError(ERROR_CODES.FILE_TOO_LARGE, ERROR_MESSAGES[ERROR_CODES.FILE_TOO_LARGE]);
  }

  if (!allowedTypes.includes(file.type)) {
    throw new ChatError(ERROR_CODES.INVALID_FILE_TYPE, ERROR_MESSAGES[ERROR_CODES.INVALID_FILE_TYPE]);
  }
};

export default {
  ChatError,
  ERROR_CODES,
  ERROR_MESSAGES,
  handleError,
  showErrorAlert,
  withRetry,
  isNetworkError,
  validateMessage,
  validateFile,
};
