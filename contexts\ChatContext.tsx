import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { ChatState, ChatConversation, ChatMessage, TypingUser, RealtimeMessageEvent } from '@/types/chat';
import { supabase, getCurrentUser, getConversations } from '@/lib/supabase';

// Action types
type ChatAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | undefined }
  | { type: 'SET_CONVERSATIONS'; payload: ChatConversation[] }
  | { type: 'ADD_CONVERSATION'; payload: ChatConversation }
  | { type: 'UPDATE_CONVERSATION'; payload: ChatConversation }
  | { type: 'SET_CURRENT_CONVERSATION'; payload: ChatConversation | undefined }
  | { type: 'SET_MESSAGES'; payload: { conversationId: string; messages: ChatMessage[] } }
  | { type: 'ADD_MESSAGE'; payload: { conversationId: string; message: ChatMessage } }
  | { type: 'UPDATE_MESSAGE'; payload: { conversationId: string; message: ChatMessage } }
  | { type: 'SET_TYPING_USERS'; payload: { conversationId: string; users: TypingUser[] } }
  | { type: 'ADD_TYPING_USER'; payload: { conversationId: string; user: TypingUser } }
  | { type: 'REMOVE_TYPING_USER'; payload: { conversationId: string; userId: string } };

// Initial state
const initialState: ChatState = {
  conversations: [],
  currentConversation: undefined,
  messages: {},
  typingUsers: {},
  loading: false,
  error: undefined,
};

// Reducer
function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };

    case 'SET_ERROR':
      return { ...state, error: action.payload };

    case 'SET_CONVERSATIONS':
      return { ...state, conversations: action.payload };

    case 'ADD_CONVERSATION':
      return {
        ...state,
        conversations: [action.payload, ...state.conversations],
      };

    case 'UPDATE_CONVERSATION':
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.id ? action.payload : conv
        ),
      };

    case 'SET_CURRENT_CONVERSATION':
      return { ...state, currentConversation: action.payload };

    case 'SET_MESSAGES':
      return {
        ...state,
        messages: {
          ...state.messages,
          [action.payload.conversationId]: action.payload.messages,
        },
      };

    case 'ADD_MESSAGE':
      const { conversationId, message } = action.payload;
      const existingMessages = state.messages[conversationId] || [];
      return {
        ...state,
        messages: {
          ...state.messages,
          [conversationId]: [...existingMessages, message],
        },
      };

    case 'UPDATE_MESSAGE':
      const { conversationId: convId, message: updatedMessage } = action.payload;
      const messages = state.messages[convId] || [];
      return {
        ...state,
        messages: {
          ...state.messages,
          [convId]: messages.map(msg =>
            msg.id === updatedMessage.id ? updatedMessage : msg
          ),
        },
      };

    case 'SET_TYPING_USERS':
      return {
        ...state,
        typingUsers: {
          ...state.typingUsers,
          [action.payload.conversationId]: action.payload.users,
        },
      };

    case 'ADD_TYPING_USER':
      const currentTyping = state.typingUsers[action.payload.conversationId] || [];
      const userExists = currentTyping.some(user => user.id === action.payload.user.id);
      if (userExists) return state;

      return {
        ...state,
        typingUsers: {
          ...state.typingUsers,
          [action.payload.conversationId]: [...currentTyping, action.payload.user],
        },
      };

    case 'REMOVE_TYPING_USER':
      const typingUsers = state.typingUsers[action.payload.conversationId] || [];
      return {
        ...state,
        typingUsers: {
          ...state.typingUsers,
          [action.payload.conversationId]: typingUsers.filter(
            user => user.id !== action.payload.userId
          ),
        },
      };

    default:
      return state;
  }
}

// Context
interface ChatContextType {
  state: ChatState;
  dispatch: React.Dispatch<ChatAction>;
  loadConversations: () => Promise<void>;
  subscribeToConversationUpdates: () => void;
  unsubscribeFromAll: () => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Provider
interface ChatProviderProps {
  children: ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(chatReducer, initialState);
  const subscriptionsRef = React.useRef<any[]>([]);

  const loadConversations = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: undefined });

      const user = await getCurrentUser();
      if (!user) {
        throw new Error('No authenticated user');
      }

      const { data, error } = await getConversations(user.id);
      if (error) {
        throw error;
      }

      if (data) {
        dispatch({ type: 'SET_CONVERSATIONS', payload: data });
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load conversations' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const subscribeToConversationUpdates = () => {
    // Subscribe to conversation changes
    const conversationSubscription = supabase
      .channel('conversations')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'conversations',
        },
        (payload) => {
          if (payload.eventType === 'INSERT' && payload.new) {
            dispatch({ type: 'ADD_CONVERSATION', payload: payload.new as ChatConversation });
          } else if (payload.eventType === 'UPDATE' && payload.new) {
            dispatch({ type: 'UPDATE_CONVERSATION', payload: payload.new as ChatConversation });
          }
        }
      )
      .subscribe();

    subscriptionsRef.current.push(conversationSubscription);

    // Subscribe to message changes for updating last message in conversations
    const messageSubscription = supabase
      .channel('messages')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
        },
        (payload) => {
          if (payload.new) {
            const message = payload.new as ChatMessage;
            
            // Update the conversation's last message
            dispatch({
              type: 'UPDATE_CONVERSATION',
              payload: {
                ...state.conversations.find(c => c.id === message.conversation_id)!,
                last_message: message,
                updated_at: message.created_at,
              },
            });
          }
        }
      )
      .subscribe();

    subscriptionsRef.current.push(messageSubscription);
  };

  const unsubscribeFromAll = () => {
    subscriptionsRef.current.forEach(subscription => {
      subscription.unsubscribe();
    });
    subscriptionsRef.current = [];
  };

  // Load conversations on mount
  useEffect(() => {
    loadConversations();
    subscribeToConversationUpdates();

    return () => {
      unsubscribeFromAll();
    };
  }, []);

  const value: ChatContextType = {
    state,
    dispatch,
    loadConversations,
    subscribeToConversationUpdates,
    unsubscribeFromAll,
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};

// Hook
export const useChat = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};

export default ChatContext;
