import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Alert,
  RefreshControl,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { ChatHeader, MessageBubble, MessageInput, TypingIndicator } from '@/components/chat';
import { ChatMessage, ChatConversation, ChatUser, TypingUser } from '@/types/chat';
import { chatTheme } from '@/constants/theme';
import {
  shouldShowAvatar,
  shouldShowTimestamp,
  shouldShowSenderName,
  groupMessagesByDate,
} from '@/utils/chat';
import {
  supabase,
  getCurrentUser,
  getMessages,
  sendMessage,
  updateTypingStatus,
  subscribeToMessages,
  subscribeToTyping,
  markMessageAsRead,
} from '@/lib/supabase';
import * as Haptics from 'expo-haptics';

export default function ChatScreen() {
  const params = useLocalSearchParams<{
    id: string;
    name?: string;
    isGroup?: string;
  }>();

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [conversation, setConversation] = useState<ChatConversation | null>(null);
  const [currentUser, setCurrentUser] = useState<ChatUser | null>(null);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [replyTo, setReplyTo] = useState<ChatMessage | undefined>();

  const flatListRef = useRef<FlatList>(null);
  const messageSubscriptionRef = useRef<any>(null);
  const typingSubscriptionRef = useRef<any>(null);

  // Load initial data
  useEffect(() => {
    loadInitialData();
    return () => {
      // Cleanup subscriptions
      if (messageSubscriptionRef.current) {
        messageSubscriptionRef.current.unsubscribe();
      }
      if (typingSubscriptionRef.current) {
        typingSubscriptionRef.current.unsubscribe();
      }
    };
  }, [params.id]);

  // Setup real-time subscriptions
  useEffect(() => {
    if (currentUser && params.id) {
      setupSubscriptions();
    }
  }, [currentUser, params.id]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Load current user
      const user = await getCurrentUser();
      if (!user) {
        router.replace('/sign-in');
        return;
      }

      const { data: userProfile } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (userProfile) {
        setCurrentUser(userProfile);
      }

      // Load conversation details
      const { data: conversationData } = await supabase
        .from('conversations')
        .select(`
          *,
          conversation_participants(
            user_id,
            users(*)
          )
        `)
        .eq('id', params.id)
        .single();

      if (conversationData) {
        const participants = conversationData.conversation_participants?.map(
          (cp: any) => cp.users
        ) || [];
        
        setConversation({
          ...conversationData,
          participants,
        });
      }

      // Load messages
      await loadMessages();
    } catch (error) {
      console.error('Error loading chat data:', error);
      Alert.alert('Error', 'Failed to load chat');
    } finally {
      setLoading(false);
    }
  };

  const loadMessages = async (offset = 0) => {
    try {
      const { data, error } = await getMessages(params.id, 50, offset);
      
      if (error) {
        throw error;
      }

      if (data) {
        const messagesWithOwnership = data.map(msg => ({
          ...msg,
          isOwn: msg.sender_id === currentUser?.id,
        }));

        if (offset === 0) {
          setMessages(messagesWithOwnership.reverse());
        } else {
          setMessages(prev => [...messagesWithOwnership.reverse(), ...prev]);
        }

        // Mark messages as read
        if (currentUser) {
          data.forEach(msg => {
            if (msg.sender_id !== currentUser.id) {
              markMessageAsRead(msg.id, currentUser.id);
            }
          });
        }
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  const setupSubscriptions = () => {
    // Subscribe to new messages
    messageSubscriptionRef.current = subscribeToMessages(
      params.id,
      (payload) => {
        if (payload.eventType === 'INSERT' && payload.new) {
          const newMessage = {
            ...payload.new,
            isOwn: payload.new.sender_id === currentUser?.id,
          };
          setMessages(prev => [...prev, newMessage]);
          
          // Auto-scroll to bottom for new messages
          setTimeout(() => {
            flatListRef.current?.scrollToEnd({ animated: true });
          }, 100);

          // Mark as read if not own message
          if (currentUser && newMessage.sender_id !== currentUser.id) {
            markMessageAsRead(newMessage.id, currentUser.id);
          }
        }
      }
    );

    // Subscribe to typing indicators
    typingSubscriptionRef.current = subscribeToTyping(
      params.id,
      (payload) => {
        if (payload.new && payload.new.user_id !== currentUser?.id) {
          const { user_id, is_typing } = payload.new;
          
          setTypingUsers(prev => {
            const filtered = prev.filter(user => user.id !== user_id);
            
            if (is_typing) {
              // Add user to typing list
              const typingUser = conversation?.participants?.find(p => p.id === user_id);
              if (typingUser) {
                return [...filtered, {
                  id: typingUser.id,
                  username: typingUser.username,
                  avatar_url: typingUser.avatar_url,
                }];
              }
            }
            
            return filtered;
          });
        }
      }
    );
  };

  const handleSendMessage = async (text: string, replyToId?: string) => {
    if (!currentUser) return;

    try {
      const messageData = {
        conversation_id: params.id,
        sender_id: currentUser.id,
        content: text,
        message_type: 'text' as const,
        reply_to: replyToId,
      };

      const { data, error } = await sendMessage(messageData);
      
      if (error) {
        throw error;
      }

      // Clear reply
      setReplyTo(undefined);
      
      // Haptic feedback
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message');
    }
  };

  const handleTypingChange = useCallback(async (isTyping: boolean) => {
    if (!currentUser) return;

    try {
      await updateTypingStatus(params.id, currentUser.id, isTyping);
    } catch (error) {
      console.error('Error updating typing status:', error);
    }
  }, [currentUser, params.id]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadMessages(messages.length);
    setRefreshing(false);
  }, [messages.length]);

  const handleMessageLongPress = (message: ChatMessage) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    const options = ['Reply', 'Copy', 'Cancel'];
    if (message.isOwn) {
      options.splice(2, 0, 'Delete');
    }

    Alert.alert(
      'Message Options',
      '',
      options.map(option => ({
        text: option,
        style: option === 'Delete' ? 'destructive' : option === 'Cancel' ? 'cancel' : 'default',
        onPress: () => {
          switch (option) {
            case 'Reply':
              setReplyTo(message);
              break;
            case 'Copy':
              // TODO: Copy to clipboard
              break;
            case 'Delete':
              // TODO: Delete message
              break;
          }
        },
      }))
    );
  };

  const renderMessage = ({ item, index }: { item: ChatMessage; index: number }) => {
    const nextMessage = messages[index + 1];
    const previousMessage = messages[index - 1];
    
    return (
      <MessageBubble
        message={item}
        isOwn={item.isOwn || false}
        showAvatar={shouldShowAvatar(item, nextMessage, conversation?.is_group)}
        showTimestamp={shouldShowTimestamp(item, nextMessage)}
        showSenderName={shouldShowSenderName(item, previousMessage, conversation?.is_group)}
        onLongPress={() => handleMessageLongPress(item)}
      />
    );
  };

  if (!conversation || !currentUser) {
    return (
      <View className="flex-1 items-center justify-center" style={{ backgroundColor: chatTheme.colors.background }}>
        {/* Loading state */}
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      className="flex-1"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ backgroundColor: chatTheme.colors.background }}
    >
      {/* Header */}
      <ChatHeader
        conversation={conversation}
        currentUserId={currentUser.id}
        onBack={() => router.back()}
        onProfilePress={() => {
          // TODO: Navigate to profile/group info
        }}
        typingUsers={typingUsers}
      />

      {/* Messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ paddingVertical: 8 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={chatTheme.colors.primary}
            colors={[chatTheme.colors.primary]}
          />
        }
        onContentSizeChange={() => {
          if (messages.length > 0) {
            flatListRef.current?.scrollToEnd({ animated: false });
          }
        }}
        showsVerticalScrollIndicator={false}
      />

      {/* Typing indicator */}
      <TypingIndicator
        typingUsers={typingUsers}
        visible={typingUsers.length > 0}
      />

      {/* Message input */}
      <MessageInput
        onSendMessage={handleSendMessage}
        onTypingChange={handleTypingChange}
        replyTo={replyTo}
        onCancelReply={() => setReplyTo(undefined)}
      />
    </KeyboardAvoidingView>
  );
}
